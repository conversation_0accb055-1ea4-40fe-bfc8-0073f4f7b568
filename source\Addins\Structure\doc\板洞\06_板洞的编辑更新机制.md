## 板洞的参数更新机制
板洞的参数更新主要通过`StructurePlateHole::Edit`方法实现：

```cpp
StructurePlateHole* StructurePlateHole::Edit(const StructurePlateHoleEditElementInput* pInput)
{

    IDocument* pDoc = pInput->Document;

    ElementId instanceId = pInput->InstanceId;
    IInstance* pInstance = quick_cast<IInstance>(pDoc->GetElement(instanceId));

    const IElementPosition* posBehavior = pInstance->GetElementPosition();

    const IPositionPointRelativeHostPoint2d* posPointRelativeHostPoint2d = quick_cast<const IPositionPointRelativeHostPoint2d>(posBehavior->GetPositionGeometry());
    // 获取板洞对象
    StructurePlateHole* pPlateHole = StructurePlateHole::Get(pInstance);

    std::vector<IModelLine*> profileCurves = pInput->ProfileCurves;

    // 更新板洞轮廓线
    bool bOk = pPlateHole->UpdatePlateHoleProfiles(profileCurves);

    // 更新轮廓线的坐标系
    std::vector<OwnerPtr<ICurve3d>> profileICurvesOwner;
    std::vector<const ICurve3d*> profileICurves;
    for (auto& pModelLine : profileCurves)
    {
        OwnerPtr<ICurve3d> opCurve3d = pModelLine->GetGeometryCurve();
        profileICurves.push_back(opCurve3d.get());
        profileICurvesOwner.push_back(TransferOwnership(opCurve3d));
    }

    // 保持原有插入点的位置
    Vector3d insertPt = posPointRelativeHostPoint2d->GetWorldOrigin();
    Coordinate3d baseCoord(insertPt, posPointRelativeHostPoint2d->GetDirectionX(), posPointRelativeHostPoint2d->GetDirectionY());
    OwnerPtr<IPolygon> opPolygon = StructureProfileInstanceUtils::CalculateProfilePolygon(profileICurves, baseCoord);

    // 更新轮廓线
    bOk = StructureProfileInstanceUtils::SetProfileByCurveArray(opPolygon.get(), baseCoord, pInstance);

    return pPlateHole;
}
```

板洞参数更新的主要步骤包括：

1. 更新板洞轮廓线ID：通过`UpdatePlateHoleProfiles`方法更新板洞的轮廓线ID
2. 计算轮廓多边形：根据轮廓线计算多边形
3. 更新实例的轮廓参数：通过`SetProfileByCurveArray`方法更新实例的轮廓参数

### 族服务更新板洞轮廓

`SetProfileByCurveArray`是GDMP中的一个关键函数，用于将多边形轮廓设置到实例的参数中，是轮廓系统的核心方法：

```cpp
bool gcmp::StructureProfileInstanceUtils::SetProfileByCurveArray(const IPolygon* pPolygon, const Coordinate3d& baseCoord, gcmp::IInstance* pInstance)
{
    // 获取实例的坐标系参数
    OwnerPtr<IParameter> opRefCoordinateSystems = ELEMENT_PARAMETER(pInstance, CoordinateSystemBuiltInParameter);

    // 获取参数值存储对象
    IParameterValueCurveByPoints* pPVSCoordinate =
        dynamic_cast<IParameterValueCurveByPoints*>(opRefCoordinateSystems->GetParameterValueStorageFw());

    // 核心步骤：设置多边形坐标系
    bool isSuccessful = pPVSCoordinate->SetCoordinateSystemsByPolygon(pPolygon);

    // 更新实例参数
    pInstance->GetElementParameters()->SetParameter(opRefCoordinateSystems.get());

    return true;
}
```

在板洞编辑中，当用户修改板洞的轮廓线时，系统会调用此函数更新板洞实例的参数，触发计算器更新板洞的几何形状，从而实现板洞的编辑功能。

`UpdatePlateHoleProfiles`维护轮廓IModelLine：

```cpp
bool StructurePlateHole::UpdatePlateHoleProfiles(const std::vector<IModelLine*> profileCurves)
{
    std::vector<ElementId> profileIds;
    FOR_EACH(pModelLine, profileCurves)
    {
        profileIds.push_back(pModelLine->GetOwnerElement()->GetElementId());
    }
    this->SetProfileIds__(profileIds);

    return true;
}
```

这个方法将轮廓线的ID存储到板洞对象中，使板洞能够追踪其轮廓线。

## 板洞的变换更新机制

板洞支持变换操作，如镜像，通过`Transform`方法实现：

```cpp
bool gcmp::StructurePlateHole::Transform(const Matrix4d & matrix)
{
    // 该段代码只对平台GBMP起效
    if (!matrix.IsMirror())
        return true;

    Matrix4d matMirrorLocal;
    matMirrorLocal.MakeMirror(Vector3d::Zero, Vector3d::UnitY);

    IDocument* pDoc = GetDocument();

    // 对板洞边缘上的轮廓线进行镜像操作，使其与板洞边缘一致
    auto profileIds = GetProfileIds();
    FOR_EACH(id, profileIds)
    {
        IElement* pProfileLineElement = pDoc->GetElement(id);
        if (pProfileLineElement)
        {
            IElementPosition *pElementPosition = pProfileLineElement->GetElementPosition();
            if (!pElementPosition->Transform(matMirrorLocal))
                return false;
        }
    }
    return true;
}
```

当板洞被镜像时，这个方法会对板洞的轮廓线进行相应的镜像操作，确保轮廓线与板洞的形状保持一致。

## 板洞的编辑模式实现与更新机制

### 编辑模式的数据层实现

板洞的编辑模式数据层由`StructurePlateHoleEditModeBehavior`类实现：

```cpp
class StructurePlateHoleEditModeBehavior : public gcmp::NdbObjectSchematic, public IStructurePlateHoleEditModeBehavior
{
    DBOBJECT_DATA_BEGIN_AND_QUICK_CAST_FROM(gcmp, StructurePlateHoleEditModeBehavior, gcmp::NdbObjectSchematic, F12DC080-67D3-42CB-ADD1-FAA80889EF3C, gmstructure, gcmp::IStructurePlateHoleEditModeBehavior)
        DATA(ElementId, EditingFamilyId)  // 正在编辑的族ID
        DATA(ElementId, EditingInstanceTypeId)  // 正在编辑的实例类型ID
        DATA(ElementId, PresetElementId)  // 预设元素ID
        DATA(ElementId, HostId)  // 宿主板ID
    DATA_END

public:
    virtual bool StartEditing(IDocument* pDoc, bool isCreating) override;
    virtual bool EndEditing(IDocument* pDoc) override;
    virtual std::wstring GetEditModeType() const override { return StructureEditModeTypes::StructurePlateHoleModeType; }
    virtual bool IsElementEditable(const IElement* pElement) const override;
};
```

这个类继承自`NdbObjectSchematic`，实现了`IStructurePlateHoleEditModeBehavior`接口，`IStructurePlateHoleEditModeBehavior`的父类和祖宗类分别是`IStructureEditModeBehavior`和`IEditModeBehavior`。

下图展示了`StructurePlateHoleEditModeBehavior`的继承关系：

```mermaid
classDiagram
    IEditModeBehavior <|-- IStructureEditModeBehavior : 继承
    IStructureEditModeBehavior <|-- IStructurePlateHoleEditModeBehavior : 继承
    IStructurePlateHoleEditModeBehavior <|.. StructurePlateHoleEditModeBehavior : 实现
    NdbObjectSchematic <|-- StructurePlateHoleEditModeBehavior : 继承

    class IEditModeBehavior {
        <<interface>>
        +基础编辑模式接口
    }
    class IStructureEditModeBehavior {
        <<interface>>
        +结构构件编辑模式接口
    }
    class IStructurePlateHoleEditModeBehavior {
        <<interface>>
        +板洞编辑模式接口
    }
    class StructurePlateHoleEditModeBehavior {
        +EditingFamilyId: ElementId
        +EditingInstanceTypeId: ElementId
        +PresetElementId: ElementId
        +HostId: ElementId
        +StartEditing(IDocument*, bool): bool
        +EndEditing(IDocument*): bool
        +GetEditModeType(): std::wstring
        +IsElementEditable(const IElement*): bool
    }
    class NdbObjectSchematic {
        <<abstract>>
        +数据存储基类
    }
```

这种继承结构使得板洞编辑模式能够复用通用的编辑模式功能，同时添加特定于板洞的专用功能。

`IStructurePlateHoleEditModeBehavior`主要负责`IEditModeBehavior`的内容：

- 存储编辑过程中的数据（族ID、实例类型ID、宿主板ID等）
- 控制编辑模式的启动和结束
- 判断元素是否可编辑

UI交互部分由`UIStructurePlateHoleProfileEditModeBehavior`类实现，详见[板洞的交互功能](06_板洞的交互功能.md)中的相关内容。

### 编辑模式的启动参数

板洞创建和编辑使用相同的编辑模式类，但通过不同的参数区分：

```cpp
// 创建板洞时
pProfileEditMode->StartEditing(pDoc, ElementId::InvalidID, true, false);

// 编辑板洞时
pStructurePlateHoleEditMode->StartEditing(pDoc, editedId, false, false);
```

其中关键区别是：

- 创建板洞时，`isCreating`参数为`true`，编辑元素ID为`InvalidID`
- 编辑板洞时，`isCreating`参数为`false`，编辑元素ID为现有板洞的ID

### 编辑模式的状态更新

板洞的编辑模式通过`StructurePlateHoleEditModeBehavior`类管理，它负责处理编辑过程中的状态更新：

```cpp
bool StructurePlateHoleEditModeBehavior::StartEditing(gcmp::IDocument* pDoc, bool isCreating)
{
    IEditMode* pProfileEditMode = IEditMode::Get(pDoc, GetEditModeType());
    pProfileEditMode->SetIsCreating(isCreating);

    return true;
}

bool StructurePlateHoleEditModeBehavior::EndEditing(gcmp::IDocument* pDoc)
{
    this->DeletePresetElementId(pDoc);

    return true;
}
```

编辑模式还负责处理父元素删除时的更新：

```cpp
void StructurePlateHoleEditModeBehavior::UpdateForWeakParentDeletion(gcmp::IDocument* pDoc, const std::set<gcmp::ElementId>& deletedElementIds)
{
    IEditMode* pProfileEditMode = IEditMode::Get(pDoc, GetEditModeType());

    if (deletedElementIds.find(pProfileEditMode->GetEditingElementId()) != deletedElementIds.end())
    {
    }

    if (deletedElementIds.find(GetPresetElementId()) != deletedElementIds.end())
    {
        SetPresetElementId__(ElementId::InvalidID);
    }

    if (deletedElementIds.find(GetEditingFamilyId()) != deletedElementIds.end())
    {
        SetEditingFamilyId__(ElementId::InvalidID);
    }

    if (deletedElementIds.find(GetEditingInstanceTypeId()) != deletedElementIds.end())
    {
        SetEditingInstanceTypeId__(ElementId::InvalidID);
    }

    if (deletedElementIds.find(GetHostId()) != deletedElementIds.end())
    {
        SetHostId__(ElementId::InvalidID);
    }
}
```

这个方法确保当相关元素被删除时，编辑模式能够正确地更新其状态，避免引用已删除的元素。

## 轮廓线的处理机制

### 进入编辑模式时的轮廓线处理

在板洞编辑过程中，轮廓线(IModelLine)的处理是一个关键环节。当进入板洞编辑模式时，系统会执行以下操作：

1. **隐藏原始板洞实例**：

   ```cpp
   IElementStatus* pStatus = pInstance->GetStatus();
   pStatus->SetIsVisible(false); // 隐藏原实例
   ```

2. **创建新的轮廓线**：系统会根据原有板洞的轮廓创建新的IModelLine对象，用于编辑操作。这些轮廓线通过`ProfileUsedModelLineUtils`类进行管理：

   ```cpp
   std::vector<IModelLine*> profileCurves;
   bool bOk = StructureProfileInstanceUtils::GenerateProfileCurvesFromPolygon(pDoc, pPolygon, associatedPlaneId, profileCurves);
   ```

3. **标记轮廓线用途**：创建的轮廓线会被标记为用于轮廓编辑，通过`ProfileUsedModelLineUtils::CreateModelLineUsedForProfile`方法实现：

   ```cpp
   // 注册外部数据，标记轮廓线已经被使用
   pModelLine->GetExternalDatas()->RegisterExternalData(ED_KEY_MODEL_LINE_USED_PROFILE,
       NEW_AS_OWNER_PTR(ModelLineUsedProfileData, pDoc, pModelLine->GetOwnerElement()->GetElementId()));
   ```

4. **关联轮廓线与实例**：通过`SetAssociatedInstanceIdForModelLines`方法将轮廓线与板洞实例关联：

   ```cpp
   ProfileUsedModelLineUtils::SetAssociatedInstanceIdForModelLines(profileCurves, pInstance->GetElementId());
   ```

### 退出编辑模式时的轮廓线处理

当完成编辑并退出编辑模式时，系统会执行以下操作：

1. **收集轮廓线信息**：系统会收集当前文档中的所有轮廓线：

   ```cpp
   std::vector<IModelLine*> profileCurves;
   ProfileUsedModelLineUtils::GetStructureProfileModelLinesInCurrDoc(pDoc, profileCurves);
   ```

2. **更新板洞轮廓线ID**：通过`UpdatePlateHoleProfiles`方法更新板洞的轮廓线ID：

   ```cpp
   bool bOk = pPlateHole->UpdatePlateHoleProfiles(profileCurves);
   ```

3. **保留轮廓线但隐藏**：与柱、梁等构件不同，板洞编辑完成后**不会删除**轮廓线，而是将它们隐藏并保持与板洞的关联：

   ```cpp
   FOR_EACH(pModelLine, profileCurves)
   {
       pModelLine->GetOwnerElement()->GetStatus()->SetIsVisible(false);
   }
   ```

4. **显示板洞实例**：编辑完成后，显示更新后的板洞实例：

   ```cpp
   IElementStatus* pStatus = pInstance->GetStatus();
   pStatus->SetIsVisible(true);
   ```

### 板洞删除时的轮廓线处理

当板洞被删除时，由于引用关系的设计，系统会自动处理相关的轮廓线：

#### 引用关系的双向处理

板洞与轮廓线之间存在双向引用关系，采用了不同的引用强度：

1. **板洞对轮廓线的弱引用**：通过`StructurePlateHole`类的`ReportParents`和`UpdateForWeakParentDeletion`方法实现：

   ```cpp
   void StructurePlateHole::ReportParents(IElementParentReporter& reporter) const
   {
       FOR_EACH(id, GetProfileIds__())
       {
           reporter.ReportWeak(id);  // 弱引用关系
       }
   }

   void StructurePlateHole::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
   {
       FOR_EACH(id, deletedElementIds)
       {
           auto it = std::find(GetProfileIdsFW__().begin(), GetProfileIdsFW__().end(), id);
           if (it != GetProfileIdsFW__().end())
           {
               GetProfileIdsFW__().erase(it);  // 轮廓线被删除时，从板洞中移除引用
           }
       }
   }
   ```

2. **轮廓线对板洞的强引用**：通过`ModelLineUsedProfileData`类的`ReportParents`方法实现：

   ```cpp
   void ModelLineUsedProfileData::ReportParents(IElementParentReporter& reporter) const
   {
       if (GetInstanceId().IsValid())
       {
           reporter.ReportStrong(GetInstanceId());  // 强引用关系
       }
   }
   ```

   而`UpdateForWeakParentDeletion`方法为空实现，因为轮廓线对板洞是强引用：

   ```cpp
   void ModelLineUsedProfileData::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
   {
       return;  // 空实现，因为是强引用
   }
   ```

   在GDMP的引用系统中，强引用具有重要意义：当被引用对象（板洞）被删除时，所有强引用它的对象（轮廓线）也会被自动删除，确保数据一致性并防止悬空引用。

#### 建立轮廓线与板洞关联

`ProfileUsedModelLineUtils::SetAssociatedInstanceIdForModelLines`方法是建立轮廓线与板洞关联的核心方法：

```cpp
void ProfileUsedModelLineUtils::SetAssociatedInstanceIdForModelLines(std::vector<IModelLine*> profileCurves, ElementId instanceId)
{
    for (auto& pIModelLine : profileCurves)
    {
        // 获取轮廓线的扩展数据
        auto pData = pIModelLine->GetExternalDatas()->FindExternalData(ED_KEY_MODEL_LINE_USED_PROFILE);

        // 转换为ModelLineUsedProfileData类型
        ModelLineUsedProfileData* pProfileData = quick_cast<ModelLineUsedProfileData>(pData);
        // 设置关联的实例ID
        pProfileData->SetInstanceId(instanceId);
    }
}
```

这个方法的主要功能是：

- 遍历所有轮廓线
- 获取每条轮廓线的`ModelLineUsedProfileData`扩展数据
- 将板洞的ID设置到轮廓线的扩展数据中，建立强引用关系

通过这种方式，轮廓线与板洞之间建立了明确的依赖关系：轮廓线依赖于板洞的存在，当板洞被删除时，轮廓线也会被自动删除，从而保持数据的一致性和完整性。这种设计确保了板洞删除时不会留下"孤儿"轮廓线，简化了内存管理并防止了潜在的资源泄漏。