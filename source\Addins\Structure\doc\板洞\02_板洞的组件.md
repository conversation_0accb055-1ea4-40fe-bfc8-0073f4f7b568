## 图形表达组件
板洞的图形表达通过实现`IGraphicsElementShapeCustomizableBehavior`接口来定制IInstance的图形表达。该接口允许组件获取IInstance原有的图形表达并进行定制修改，最终返回新的图形表达。

板洞的图形表达定制主要通过以下步骤实现：

1. 获取宿主板的图形表达
2. 创建板洞的切割体
3. 通过布尔运算（交集）生成板洞的最终图形
4. 设置图形的可见性和样式

这种实现方式使板洞在视觉上表现为宿主板上的一个洞口，直观地展示了板洞的形状和位置。
板洞的具体图形表达通过`StructureFloorHoleGraphicsElementShapeBehavior`类实现：

```cpp
class STRUCTURE_EXPORT StructureFloorHoleGraphicsElementShapeBehavior : public NdbObjectSchematic, public IGraphicsElementShapeCustomizableBehavior
{
    DBOBJECT_DATA_BEGIN_AND_QUICK_CAST_FROM(gcmp, StructureFloorHoleGraphicsElementShapeBehavior, gcmp::NdbObjectSchematic, 7A95E3D2-23D5-4CE8-8823-20AA7F8CB1A8, gmstructure, gcmp::IGraphicsElementShapeCustomizableBehavior)
        DATA_TRANSIENT(IElement*, pOwnerElement)
    DBOBJECT_DATA_END

public:
    StructureFloorHoleGraphicsElementShapeBehavior(IElement* pInstance);

    virtual bool SetOwnerElement(IElement* pOwnerElement) override;
    virtual void ReportInputDataIds(std::vector<RegenDataId>& oInputDataIds) const override;
    virtual OwnerPtr<IGraphicsElementShape> Customize(OwnerPtr<IGraphicsElementShape> opGRep) const override;

private:
    IInstance* GetOwnerInstance() const;
    OwnerPtr<IGraphicsBRepBody> CreateBoolIntersectOperation(IInstance* pIInstance, OwnerPtr<IGraphicsElementShape> opHostsGRep,
                                                             OwnerPtr<IGraphicsElementShape> opCutterGRep)const;
};
```

这个类实现了`IGraphicsElementShapeCustomizableBehavior`接口，用于自定义板洞的图形表达。

### 图形表达的依赖关系

`ReportInputDataIds`方法报告了图形表达依赖的数据：

```cpp
void StructureFloorHoleGraphicsElementShapeBehavior::ReportInputDataIds(std::vector<RegenDataId>& oInputDataIds) const
{
    const IInstance* pInstance = GetOwnerInstance();

    IDocument* pDoc = pInstance->GetDocument();

    const IInstance* pHostInstance = quick_cast<IInstance>(pDoc->GetElement(pInstance->GetHostInstanceId()));

    const IElementModelShape* pGrepBehavior = pHostInstance->GetElementModelShape();

    oInputDataIds.push_back(pGrepBehavior->GetGraphicsElementShapeRdId());
}
```

这里报告了板洞依赖宿主板的图形表达，当宿主板的图形表达发生变化时，会触发板洞的图形表达更新。

### 图形表达的创建

`Customize`方法实现了板洞图形表达的创建：

```cpp
OwnerPtr<IGraphicsElementShape> StructureFloorHoleGraphicsElementShapeBehavior::Customize(OwnerPtr<IGraphicsElementShape> opGRep) const
{
    IInstance* pIInstance = GetOwnerInstance();

    IDocument* pDoc = pIInstance->GetDocument();

    const IInstance* pHostInstance = quick_cast<IInstance>(pDoc->GetElement(pIInstance->GetHostInstanceId()));

    OwnerPtr<IGraphicsElementShape> opUpGRep; // Base GRep
    opUpGRep = IGraphicsElementShape::Create(opGRep->GetRenderLayer());

    // 获取宿主板的图形表达
    OwnerPtr<IGraphicsElementShape> opHostsGRep;
    {
        const IBaseGraphicsElementShapeComponent* pHostInstanceShapeComponent = pHostInstance->GetBaseGraphicsElementShapeComponent();
        const IGraphicsElementShape* pHostBaseGRep = pHostInstanceShapeComponent->GetBaseGraphicsElementShape();

        if (pHostInstance->GetBasicInformation()->GetCategoryUid().HasSameValue(BuiltInCategoryUniIdentities::BICU_STRUCTURE_FLOOR))
        {
            opHostsGRep = StructureInstanceGrepUtil::GenerateFloorGrepFromBaseGrep(pHostInstance, pHostBaseGRep);
        }
    }

    // 获取板洞的切割体
    OwnerPtr<IGraphicsElementShape> opClonedCutterGRep;
    {
        const IGeometryRelationshipComponent* pGeometryRepCpt = pIInstance->GetGeometryRelationshipComponent();
        const IOpeningGraphicsElementShapeComponent* pCutterGRepComponent = pGeometryRepCpt->GetOpeningGraphicsElementShapeComponent();
        const IGraphicsElementShape* pCutterGRep = pCutterGRepComponent->GetOpeningGraphicsElementShape();
        opClonedCutterGRep = TransferOwnershipCast<IGraphicsElementShape>(pCutterGRep->Clone());
        StructureInstanceGrepUtil::TransformGRep(opClonedCutterGRep.get(), pIInstance);
    }

    // 创建布尔运算，实现打洞效果
    OwnerPtr<IGraphicsBRepBody> opIntersectBody;
    opIntersectBody = CreateBoolIntersectOperation(pIInstance, TransferOwnership(opHostsGRep),
                                                   TransferOwnership(opClonedCutterGRep));
    if (opIntersectBody)
    {
        opUpGRep->AddChild(TransferOwnership(opIntersectBody));
        opUpGRep->SetElementId(pIInstance->GetElementId());
        opUpGRep->SetVisibility(GraphicsNodeVisibility::Always);
        opUpGRep->SetGraphicsStyleId(pDoc->GetGraphicsStyleManager()->GetGraphicsStyleIdByCategoryUid(pIInstance->GetBasicInformation()->GetCategoryUid()));
        return opUpGRep;
    }

    return opGRep;
}
```

这个方法的主要步骤是：

1. 获取宿主板的图形表达
2. 获取板洞的切割体
3. 通过布尔运算（交集）生成板洞的最终图形
4. 设置图形的可见性和样式

### 布尔运算的实现

板洞通过布尔运算实现打洞效果：

```cpp
OwnerPtr<IGraphicsBRepBody> StructureFloorHoleGraphicsElementShapeBehavior::CreateBoolIntersectOperation(IInstance* pIInstance, OwnerPtr<IGraphicsElementShape> opHostsGRep, OwnerPtr<IGraphicsElementShape> opCutterGRep) const
{
    // 获取切割体和宿主体
    OwnerPtr<IGraphicsBRepBody> opCutterBody = GetGRepBody(pIInstance->GetDocument(), opCutterGRep.get());
    OwnerPtr<IGraphicsBRepBody> opHostBody = GetGRepBody(pIInstance->GetDocument(), opHostsGRep.get());

    // 执行布尔交集运算
    OwnerPtr<IGraphicsBRepBody> opIntersectBody = GcmpElementModelingOperator::BooleanIntersect(opHostBody.get(), opCutterBody.get());

    return opIntersectBody;
}
```

这个方法通过布尔交集运算，计算切割体和宿主体的交集，生成板洞的最终图形。

## 打洞关系组件
板洞使用GDMP的打洞关系组件来实现打洞功能：

```cpp
StructurePlateHole* StructurePlateHole::Create(const StructurePlateHoleElementInput* pInput)
{
    // ...
    // 创建打洞关系
    IElementOpening::Create(pDoc, pInstance->GetElementId(), pHostInstance->GetElementId());
    // ...
}
```

这里使用了`IElementOpening`接口创建板洞与宿主板之间的打洞关系。

板洞使用了以下打洞相关的组件：

1. `IOpeningGraphicsElementShapeComponent`：打洞图形表达组件，提供打洞的图形数据
2. `IOpenedGraphicsElementShapeComponent`：被打洞图形表达组件，提供被打洞的图形数据
3. `IGeometryRelationshipComponent`：几何关系组件，管理打洞关系

## 定位组件
板洞使用`IElementPosition`组件来定义其位置：

```cpp
// 创建板洞的定位方式
StructurePlateHole* StructurePlateHole::Create(const StructurePlateHoleElementInput* pInput)
{
    // ...
    IElementPosition::CreatePointRelativeHostPoint2dOnSingleAssociatedPlanePositionBehavior(
        pInstance,
        IElementPosition::GetAssociatedPlaneId(pHostInstance),
        polygonCoord.GetOrigin(),
        polygonCoord.GetX(),
        polygonCoord.GetY()
    );
    // ...
```

板洞采用了基于宿主的点式定位方式（PointRelativeHostPoint2dOnSingleAssociatedPlane），其特点是：

1. 板洞的位置相对于宿主板的位置
2. 板洞的定位点在宿主板的平面上
3. 当宿主板移动时，板洞会跟随移动

## 参数组件
板洞实现了多个参数重载器（Parameter Override），用于自定义参数的行为：

### 参数重载器的添加

```cpp
// 添加参数重载器
pElementParameters->AddIndividualParameterOverride(NEW_AS_OWNER_PTR(StructurePlateHoleLevelOverride));
pElementParameters->AddIndividualParameterOverride(NEW_AS_OWNER_PTR(StructurePlateHoleTopOffsetOverride));
pElementParameters->AddIndividualParameterOverride(NEW_AS_OWNER_PTR(StructurePlateHoleStoreyOverride));
```

StructurePlateHoleLevelOverride重载器控制顶部关联标高参数的可见性，将该参数设置为不可见，因为板洞的标高由宿主板决定。

StructurePlateHoleTopOffsetOverride重载器控制顶部偏移参数的可见性，将该参数设置为不可见，因为板洞的标高由宿主板决定。

StructurePlateHoleStoreyOverride重载器控制楼层参数的可见性和可修改性，将楼层参数设置为可见但不可修改，因为板洞的楼层由宿主板决定。

这些参数重载器确保板洞的某些参数（如标高和楼层）与宿主板保持一致，同时提供了适当的用户界面体验。