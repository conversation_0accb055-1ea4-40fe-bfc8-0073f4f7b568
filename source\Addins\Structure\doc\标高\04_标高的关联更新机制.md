## 标高的父子关系管理机制

标高图元在GDMP中实现了父子关系管理机制，通过`ReportParents`和`UpdateForWeakParentDeletion`方法来管理与其他图元的依赖关系。

### Strong Parent vs Weak Parent概念

GDMP中的图元依赖关系分为两种类型：

1. **Strong Parent（强父图元）**：当强父图元被删除时，当前图元也会被系统自动删除
2. **Weak Parent（弱父图元）**：当弱父图元被删除时，当前图元会收到通知，可以进行相应的处理而不被删除

### 标高图元的父子关系实现

#### NewLevel实现

标高图元主要与关联平面建立强引用关系：

<augment_code_snippet path="source/GdmpLab/Db/Model/Level/NewLevel.cpp" mode="EXCERPT">
````cpp
void gcmp::NewLevel::ReportParents(IElementParentReporter& reporter) const
{
    const IElement* pElement = GetOwnerElement();
    const IElementPosition *posBehavior = pElement->GetElementPosition();
    if (posBehavior != nullptr)
    {
        reporter.ReportStrong(posBehavior->GetBaseAssociatedPlaneId());
    }
    // 处理参数绑定的依赖关系...
}
````
</augment_code_snippet>

<augment_code_snippet path="source/GdmpLab/Db/Model/Level/NewLevel.cpp" mode="EXCERPT">
````cpp
void gcmp::NewLevel::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    // 参数绑定，并且绑定的不是族参数时
    IElement* pElement = GetOwnerElement();
    IElementParameterBindings* pBehavior = pElement->GetElementParameters()->GetElementParameterBindings();
    if (pBehavior)
    {
        auto& bindingInfos = pBehavior->GetParameterBindings();
        for (int ii = (int)bindingInfos.size(); ii > 0; --ii)
        {
            const IParameterBinding* info = bindingInfos[ii - 1].get();
            // 处理参数绑定的弱引用删除...
        }
    }
}
````
</augment_code_snippet>

#### NewLevelType实现

标高类型与所属图元建立强引用关系：

<augment_code_snippet path="source/GdmpLab/Db/Model/Level/NewLevelType.cpp" mode="EXCERPT">
````cpp
void gcmp::NewLevelType::ReportParents(IElementParentReporter& reporter) const
{
    reporter.ReportStrong(GetOwnerElement()->GetElementId());
}

void gcmp::NewLevelType::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    return;
}
````
</augment_code_snippet>

### 标高的依赖关系特点

#### 强引用关系

1. **关联平面依赖**：标高对其关联平面采用强引用，确保平面删除时标高也被删除
2. **类型依赖**：标高类型对所属图元采用强引用

#### 弱引用关系

1. **参数绑定依赖**：标高对参数绑定的源图元采用弱引用，源图元删除时清理绑定关系

### 父子关系管理的调用流程

```mermaid
sequenceDiagram
    participant System as GDMP系统
    participant Level as 标高
    participant Plane as 关联平面
    participant Binding as 参数绑定

    Note over System: 依赖关系收集阶段
    System->>Level: ReportParents()
    Level->>System: ReportStrong(关联平面ID)
    Level->>System: ReportWeak(参数绑定源图元ID)

    Note over System: 弱父图元删除阶段
    System->>Binding: 删除参数绑定源图元
    System->>Level: UpdateForWeakParentDeletion(删除的ID集合)
    Level->>Level: 清理无效的参数绑定
```

### 设计特点

标高的父子关系管理具有以下特点：

1. **强依赖设计**：标高对关联平面采用强引用，确保几何一致性
2. **参数绑定处理**：对参数绑定采用弱引用，支持动态绑定管理
3. **类型安全**：标高类型与图元的强引用确保类型定义的完整性

## 标高的关联更新机制
标高在GDMP中是一个基础的参考元素，许多构件（如墙体、楼板、墙洞等）都依赖于标高。当标高发生变化时，这些依赖构件需要自动更新，以保持模型的一致性。这种关联更新机制是通过GDMP的计算器系统实现的。

### 标高计算器概述
GDMP中的计算器是实现关联更新的核心机制。

标高相关的计算器主要包括：

1. **标高夹点计算器**：更新标高夹点的位置
2. **视图原点计算器**：更新依赖于标高的视图原点高度
3. **构件关联标高计算器**：更新构件与标高的关联关系

### 标高夹点计算器
标高夹点是用于编辑标高的交互点，通过`NewLevelShapeHandlerGRepCalculator`计算器实现：

```cpp
class NewLevelShapeHandlerGRepCalculator: public gcmp::GbmpCalculatorBase
{
    DECLARE_CALCULATOR(NewLevelShapeHandlerGRepCalculator, IElementShapeHandle)

public:
    NewLevelShapeHandlerGRepCalculator(IDocument* pDoc, const RegenDataId& outputDataId)
        : GbmpCalculatorBase(pDoc, outputDataId){}

    virtual void ReportInputDataIds(std::vector<RegenDataId> & dataIds) const override
    {
        IElementShapeHandle* pAuxiliaryPoint = GetElement<IElementShapeHandle>();
        IGenericElement* pGenericElement = dynamic_cast<IGenericElement*>(GetDocument()->GetElement(pAuxiliaryPoint->GetMasterId()));
        NewLevel* pLevel = quick_cast<NewLevel>(pGenericElement->GetExternalObject());

        // 报告依赖的数据源
        dataIds.push_back(pAuxiliaryPoint->GetMasterIdRdIdForBehavior());
        dataIds.push_back(pLevel->GetReferencePlaneRdId());
    }

    virtual void Execute() override
    {
        IElementShapeHandle* pAuxiliaryPoint = GetElement<IElementShapeHandle>();

        // 触发IElementShapeHandle的数据修改，刷新Handle的显示
        IElementShapeHandleBehavior* handleBehavior = pAuxiliaryPoint->GetElementShapeHandleBehavior();
        handleBehavior->UpdatePosition();
    }
};
```

这个计算器的主要作用是在标高参考平面发生变化时，更新标高夹点的位置。计算器的注册是在`NewLevelShapeHandleBehavior::GetCalculators`方法中完成的：

```cpp
void NewLevelShapeHandleBehavior::GetCalculators(ICalculatorCollection* calculators) const
{
    IElementShapeHandle* pElementShapeHandle = GetElementShapeHandle();

    // 注册计算器
    ADD_CALCULATOR(NewLevelShapeHandlerGRepCalculator,
                  pElementShapeHandle->GetDocument(),
                  pElementShapeHandle->GetPositionRdIdForBehavior());
}
```
## 其他图元和标高相关的计算器

很多图元都依赖标高，标高发生变化时需要关联更新。下面以视图和墙等构件为例，介绍与标高相关的计算器的实现。

### 视图原点计算器
视图（如平面视图、立面视图等）的原点高度通常依赖于标高的高度。当标高高度发生变化时，相关视图的原点高度需要自动更新。这是通过`ModelViewOriginCalculator`计算器实现的：

```cpp
class ModelViewOriginCalculator: public gcmp::GbmpCalculatorBase
{
    DECLARE_CALCULATOR(ModelViewOriginCalculator, IModelView)

public:
    ModelViewOriginCalculator(IDocument* pDoc, const RegenDataId& outputDataId)
        : GbmpCalculatorBase(pDoc, outputDataId){}

    virtual void ReportInputDataIds(std::vector<RegenDataId> & dataIds) const override
    {
        IModelView* pModelView = GetElement<IModelView>();
        ElementId workLevelId = GbmpModelViewUtil::GetWorkLevelId(pModelView);

        // 报告依赖的标高ID
        if (workLevelId.IsValid())
        {
            ILevel* pLevel = LevelUtils::GetLevelById(GetDocument(), workLevelId);
            if (pLevel)
            {
                dataIds.push_back(pLevel->GetReferencePlaneRdId());
            }
        }
    }

    virtual void Execute() override
    {
        IModelView* pModelView = GetElement<IModelView>();
        IDocument* pDoc = GetDocument();

        // 获取工作标高
        ElementId workLevelId = GbmpModelViewUtil::GetWorkLevelId(pModelView);
        ILevel* pLevel = LevelUtils::GetLevelById(pDoc, workLevelId);

        // 更新视图原点高度
        pModelView->SetOriginHeight(pLevel->GetElevation());
    }
};
```

这个计算器的主要作用是在标高高度发生变化时，更新依赖于该标高的视图原点高度。

### 构件关联标高计算器
许多构件（如墙体、楼板、墙洞等）都需要与标高建立关联关系。当标高发生变化时，这些构件需要自动更新。以墙洞为例，墙洞的所属楼层是通过`WallHoleAssociatedLevelCalculator`计算器实现的：

```cpp
class WallHoleAssociatedLevelCalculator : public GbmpCalculatorBase
{
    DECLARE_CALCULATOR(WallHoleAssociatedLevelCalculator, IElement)
public:
    WallHoleAssociatedLevelCalculator(IDocument* pDoc, const RegenDataId& outputDataId)
        : GbmpCalculatorBase(pDoc, outputDataId) {}

    virtual void ReportInputDataIds(std::vector<RegenDataId> & oInputDatas) const override
    {
        const IInstance* pElement = GetElement<IInstance>();

        // 获取墙洞所在的墙体
        ElementId wallId = pElement->GetHostElementId();
        if (!wallId.IsValid())
            return;

        // 报告依赖的墙体ID
        oInputDatas.push_back(RegenDataId(wallId, RegenDataId::ElementId));
    }

    virtual void Execute() override
    {
        IInstance* pElement = GetElement<IInstance>();

        // 获取墙洞所在的墙体
        ElementId wallId = pElement->GetHostElementId();
        if (!wallId.IsValid())
            return;

        // 获取墙体所属的楼层
        IElement* pWall = GetDocument()->GetElement(wallId);
        IInstance* pWallInstance = quick_cast<IInstance>(pWall);
        if (!pWallInstance)
            return;

        // 更新墙洞的所属楼层
        ElementId levelId = StructureInstanceLevelUtils::GetInstanceAssociatedLevelId(pWallInstance);
        StructureInstanceLevelUtils::SetInstanceAssociatedLevelId(pElement, levelId);
    }
};
```

这个计算器的主要作用是在墙体的所属楼层发生变化时，自动更新墙洞的所属楼层。
