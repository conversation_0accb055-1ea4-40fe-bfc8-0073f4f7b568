## 计算器注册
板洞通过计算器实现与宿主板的关联更新。在`StructurePlateHole::GetCalculators`方法中注册了相关计算器：

```cpp
DECLARE_CALCULATOR_CREATOR(PlateHoleAssociatedLevelCalculator)
void StructurePlateHole::GetCalculators(ICalculatorCollection * calculators) const
{
    const IElement* pElement = this->GetOwnerElement();
    ElementId plateHoleId = pElement->GetElementId();
    const IInstance* pPlateHoleInstance = dynamic_cast<const IInstance*>(GetDocument()->GetElement(plateHoleId));
    const IStructureInstanceBuildingStoreyData* pPlateHoleStoreyData = IStructureInstanceBuildingStoreyData::Get(pPlateHoleInstance);
    RegenDataId plateHoleStoreyDataId = pPlateHoleStoreyData->GetBuildingStoreyIdRdId();
    ADD_CALCULATOR(PlateHoleAssociatedLevelCalculator, GetDocument(), plateHoleStoreyDataId);
}
```

这里注册了`PlateHoleAssociatedLevelCalculator`计算器，用于处理板洞所属楼层的关联更新。

## 计算器实现
`PlateHoleAssociatedLevelCalculator`是板洞的关联更新计算器，用于确保板洞的所属楼层与宿主板保持一致：

```cpp
class PlateHoleAssociatedLevelCalculator : public GbmpCalculatorBase
{
    DECLARE_CALCULATOR(PlateHoleAssociatedLevelCalculator, IElement)
public:
    PlateHoleAssociatedLevelCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}
    virtual void ReportInputDataIds(std::vector<RegenDataId> & oInputDatas) const override;
    virtual void Execute() override;
};
```

### 报告输入数据依赖

`ReportInputDataIds`方法报告了计算器依赖的输入数据：

```cpp
void PlateHoleAssociatedLevelCalculator::ReportInputDataIds(std::vector<RegenDataId> & oInputDatas) const
{
    const IInstance* pElement = GetElement<IInstance>();

    ElementId plateId = pElement->GetHostElementId();
    if (!plateId.IsValid())
    {
        return;
    }

    // 板洞的Host主体依赖数据收集
    {
        const IInstance* pHostPlateInstance = quick_cast<const IInstance>(GetDocument()->GetElement(plateId));
        const IStructureInstanceBuildingStoreyData* pPlateStoreyData = IStructureInstanceBuildingStoreyData::Get(pHostPlateInstance);
        RegenDataId plateStoreyDataId = pPlateStoreyData->GetBuildingStoreyIdRdId();
        oInputDatas.push_back(plateStoreyDataId);
    }
}
```

这里报告了板洞依赖的宿主板楼层数据ID，当宿主板的楼层发生变化时，会触发计算器执行。

### 执行计算

`Execute`方法执行实际的计算：

```cpp
void PlateHoleAssociatedLevelCalculator::Execute()
{
    const IInstance* pPlateHoleInstance = GetElement<IInstance>();

    ElementId plateId = pPlateHoleInstance->GetHostElementId();

    const IInstance* pHostPlateInstance = quick_cast<const IInstance>(GetDocument()->GetElement(plateId));

    const IStructureInstanceBuildingStoreyData* pPlateStoreyData = IStructureInstanceBuildingStoreyData::Get(pHostPlateInstance);
    ElementId plateLevelId = pPlateStoreyData->GetBuildingStoreyId();

    bool status = StructureInstanceLevelUtils::SetInstanceStoreyLevel(GetDocument(), pPlateHoleInstance->GetElementId(), plateLevelId);
}
```

这个方法的执行流程是：

1. 获取板洞实例
2. 获取宿主板ID
3. 获取宿主板实例
4. 获取宿主板的所属楼层
5. 将板洞的所属楼层设置为与宿主板相同

## 关联图元机制

板洞与其他图元（如轮廓线、宿主板）之间存在关联关系，这些关系通过GDMP的引用系统进行管理。板洞的关联图元机制主要通过`UpdateForWeakParentDeletion`和`ReportParents`两个函数实现。

### 板洞的引用关系报告

`ReportParents`函数用于报告板洞与其他图元之间的引用关系：

```cpp
void StructurePlateHole::ReportParents(IElementParentReporter& reporter) const
{
    FOR_EACH(id, GetProfileIds__())
    {
        reporter.ReportWeak(id);
    }
}
```

这个函数的主要作用是：

1. **报告弱引用关系**：板洞对轮廓线采用弱引用（Weak Reference）关系
2. **遍历所有轮廓线**：通过`GetProfileIds__()`获取所有轮廓线的ID，并逐一报告
3. **引用关系类型**：使用`reporter.ReportWeak()`方法表明这是一个弱引用关系

弱引用意味着当轮廓线被删除时，板洞不会自动删除，而是会更新自身状态以适应轮廓线的删除。

### 板洞对轮廓线的处理

`UpdateForWeakParentDeletion`函数用于处理当板洞弱引用的轮廓线图元被删除时的情况。这在编辑板洞轮廓时可以维护轮廓线数组。

```cpp
void StructurePlateHole::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    FOR_EACH(id, deletedElementIds)
    {
        auto it = std::find(GetProfileIdsFW__().begin(), GetProfileIdsFW__().end(), id);
        if (it != GetProfileIdsFW__().end())
        {
            GetProfileIdsFW__().erase(it);
        }
    }
}
```

这个函数的主要作用是：

1. **检查删除的图元**：遍历所有被删除的图元ID
2. **查找轮廓线引用**：检查被删除的图元是否在板洞的轮廓线列表中
3. **移除引用**：如果找到匹配的轮廓线ID，从板洞的轮廓线列表中移除该ID

当轮廓线被删除时，板洞会通过这个函数更新自己的轮廓线列表，移除对已删除轮廓线的引用，从而保持数据的一致性。

### 轮廓线对板洞的引用关系

与板洞对轮廓线的弱引用不同，轮廓线对板洞采用强引用（Strong Reference）关系，这通过`ModelLineUsedProfileData`类的`ReportParents`方法实现：

```cpp
void ModelLineUsedProfileData::ReportParents(IElementParentReporter& reporter) const
{
    if (GetInstanceId().IsValid())
    {
        reporter.ReportStrong(GetInstanceId());  // 强引用关系
    }
}
```

而`UpdateForWeakParentDeletion`方法为空实现，因为轮廓线对板洞是强引用：

```cpp
void ModelLineUsedProfileData::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    return;  // 空实现，因为是强引用
}
```

### 双向引用机制的意义

板洞与轮廓线之间的双向引用机制具有重要意义：

1. **板洞对轮廓线的弱引用**：
   - 当轮廓线被删除时，板洞会更新自身状态，移除对该轮廓线的引用
   - 板洞不会因为轮廓线的删除而自动删除
   - 这种机制允许用户灵活地修改板洞的轮廓，而不会导致板洞本身被删除

2. **轮廓线对板洞的强引用**：
   - 当板洞被删除时，所有强引用它的轮廓线也会被自动删除
   - 这确保了数据的一致性，防止出现"悬空"的轮廓线
   - 用户删除板洞时，不需要手动删除相关的轮廓线

这种双向引用机制是GDMP关联图元系统的典型应用，它既保证了数据的一致性，又提供了足够的灵活性，使用户可以方便地编辑和管理板洞及其相关图元。