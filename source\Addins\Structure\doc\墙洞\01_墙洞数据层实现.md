## 墙洞的图元定义
墙洞是指在墙体上开设的门窗等开口，在GDMP中通过IInstance+IExternalData扩展的方式实现。墙洞的类层次结构分为接口层和实现层两部分：

### 接口层：定义墙洞的基本接口

```cpp
class STRUCTURE_EXPORT IStructureWallHole : public IStructureInstance
{
public:
    virtual ~IStructureWallHole();

public:
    /// \brief 创建结构墙洞
    /// \param pInput 墙洞输入数据
    /// \return nullptr-创建失败；非nullptr-创建成功
    static IStructureWallHole* Create(const StructureWallHoleInput* pInput);

public:
    virtual WallHoleZPositioningType GetZPositioningType() const = 0;
    virtual void SetZPositioningType(WallHoleZPositioningType type) = 0;

    virtual WallHoleType GetHoleType() const = 0;
    virtual IStructureWall* GetHost() const = 0;
};
```

`IStructureWallHole`接口继承自`IStructureInstance`，定义了墙洞的基本功能：

- **创建墙洞的静态方法**：提供了创建墙洞的方法
- **获取和设置Z轴定位类型的方法**：控制墙洞在墙体上的垂直定位方式
- **获取洞口类型的方法**：获取墙洞的类型（矩形或圆形）
- **获取宿主墙体的方法**：获取墙洞所属的墙体

墙洞类型包括矩形和圆形两种：

```cpp
//洞口类型
enum class WallHoleType : int32_t
{
    Rect = 0,  // 矩形
    Circle = 1 // 圆形
};
```

Z轴定位类型定义了墙洞在墙体上的垂直定位方式：

```cpp
//Z轴定位类型
enum class WallHoleZPositioningType : int32_t
{
    Center = 0,  // 中心定位
    Bottom = 1   // 底部定位
};
```

### 实现层：实现上述接口的具体类

```cpp
class StructureWallHole : public NdbObjectSchematic, public IExternalData, public IStructureWallHole
{
    DBOBJECT_DATA_BEGIN_AND_QUICK_CAST_FROM(gcmp, StructureWallHole, gcmp::NdbObjectSchematic, 705F9AF0-DBD4-4761-A0A2-0A649141B719, gmstructure, IExternalData)
        DATA_TRANSIENT(IElement*, pOwnerElement)
        DATA(ConcreteStrengthGradeType, ConcreteStrengthGrade)
        DATA(WallHoleZPositioningType, ZPositioningType)
        DATA(WallHoleType, HoleType)
    DBOBJECT_DATA_END

    // 方法实现...
};
```

`StructureWallHole`类通过NDB数据存储系统定义了以下核心数据成员：

- `pOwnerElement`：IElement*类型，指向墙洞所属的实例图元，使用DATA_TRANSIENT标记为非持久化数据
- `ConcreteStrengthGrade`：ConcreteStrengthGradeType枚举类型，表示墙洞的混凝土强度等级
- `ZPositioningType`：WallHoleZPositioningType枚举类型，表示墙洞的Z轴定位方式
- `HoleType`：WallHoleType枚举类型，表示墙洞的类型（矩形或圆形）

## 墙洞的扩展方式

墙洞通过IExternalData扩展了IInstance实例，使其具有墙洞的特性和行为。扩展过程包括以下几个关键步骤：

创建`StructureWallHole`对象并注册为外部数据：

```cpp
// 将墙洞注册成外部数据
OwnerPtr<StructureWallHole> opWallHole = NEW_AS_OWNER_PTR(StructureWallHole);
StructureWallHole* pWallHole = opWallHole.get();
pWallHole->SetOwnerElement(pInstance);
pWallHole->SetZPositioningType__(pInput->ZPositioningType);
pWallHole->SetHoleType__(pInput->HoleType);

if (IExternalDataComponent* pExternalDataComponent = pInstance->GetExternalDataComponent())
{
    pExternalDataComponent->RegisterExternalData(opWallHole->GetClassSchema()->GetName(), TransferOwnership(opWallHole));
}
```

这一步骤将墙洞对象注册为实例的外部数据，实现IInstance+IExternalData扩展。

## 墙洞的组件

### 墙洞参数重载组件

墙洞实现了多个参数重载组件，用于自定义参数的行为和显示：

```cpp
class StructureWallHoleLevelOverride : public NdbObjectSchematic, public IParameterOverride
{
    DBOBJECT_DATA_BEGIN_AND_QUICK_CAST_FROM(gcmp, StructureWallHoleLevelOverride, gcmp::NdbObjectSchematic, EAECF1FD-A1EB-4C45-B11A-FA6EE4D87509, gmstructure, gcmp::IParameterOverride)
    DBOBJECT_DATA_END
public:
    virtual UniIdentity GetUniIdentity() const override { return PARAMETER_UID(BottomAssociatedLevelBuiltInParameter); }
    virtual bool ShouldOverrideIsModifiable() const override { return false; }
    virtual bool IsModifiable() const override { return true; }
    virtual bool ShouldOverrideIsUserVisible() const override { return false; }
    virtual bool IsUserVisible() const override { return true; }
    virtual bool ShouldOverrideGetName() const override { return true; }
    virtual std::wstring GetName() const override { return L"参照标高"; }
    // ...
};
```

主要的参数重载组件包括：

1. `StructureWallHoleLevelOverride`：重载墙洞的参照标高参数，自定义参数名称为"参照标高"
2. `StructureWallHoleBottomOffsetOverride`：重载墙洞的标高偏移参数，自定义参数名称为"标高偏移"
3. `StructureWallHoleStoreyOverride`：重载墙洞的所属楼层参数，设置为不可修改，确保墙洞的所属楼层与墙体一致

这些参数重载组件在墙洞创建过程中被添加到实例的参数系统中：

```cpp
// 参数改名
pElementParameters->AddIndividualParameterOverride(NEW_AS_OWNER_PTR(StructureWallHoleLevelOverride));
pElementParameters->AddIndividualParameterOverride(NEW_AS_OWNER_PTR(StructureWallHoleBottomOffsetOverride));
pElementParameters->AddIndividualParameterOverride(NEW_AS_OWNER_PTR(StructureWallHoleStoreyOverride));
```

### 墙洞验证器组件

墙洞验证器组件`StructureWallHoleValidator`用于验证墙洞位置的有效性，确保墙洞位置在墙体范围内：

```cpp
class StructureWallHoleValidator : public NdbObjectSchematic, public StructureInstanceHoleValidator
{
    DBOBJECT_DATA_BEGIN_AND_QUICK_CAST_FROM(gcmp, StructureWallHoleValidator, gcmp::NdbObjectSchematic, D68915B7-C6BD-417F-B418-AAA3F7704F43, gmstructure, gcmp::IBaseGraphicsElementShapeValidiator)
    DBOBJECT_DATA_END
private:
    virtual std::wstring GetErrorMessage() const override
    {
        return GBMP_TR(L"墙洞位置超出墙的范围！");
    }
    virtual bool IsStructureInstanceHole(const IInstance* pInstance) const override;
};
```

墙洞验证器组件在墙洞创建过程中被添加到实例的基础图形表达组件中：

```cpp
// 添加墙洞验证器
OwnerPtr<StructureWallHoleValidator> opWallHoleValidiator = NEW_AS_OWNER_PTR(StructureWallHoleValidator);
IBaseGraphicsElementShapeComponent* pBaseShapeComponent = pInstance->GetBaseGraphicsElementShapeComponent();
pBaseShapeComponent->SetValidator(TransferOwnership(opWallHoleValidiator));
```

验证过程在`StructureInstanceHoleValidator::ValidateBeforeCalculation`方法中被调用：

```cpp
bool StructureInstanceHoleValidator::ValidateBeforeCalculation(const IInstance* pInstance, std::wstring* pErrorMessage) const
{
    // ...
    if (IsStructureInstanceHole(pInstance))
    {
        const IInstance* pRefInstance = quick_cast<IInstance>(pDoc->GetElement(pInstance->GetHostInstanceId()));
        if (pRefInstance)
        {
            bool validity = StructureInstanceUtils::ValidateStructureInstanceHole(pRefInstance, pInstance);
            if (validity)
                return true;

            *pErrorMessage = GetErrorMessage();
            return false;
        }
    }
    // ...
}
```

墙洞位置验证通过`StructureInstanceUtils::ValidateStructureInstanceHole`方法实现：

```cpp
bool StructureInstanceUtils::ValidateStructureInstanceHole(const IInstance* pHostInstance, const IInstance* pHole)
{
    // 洞口的CutterGrep与墙的BaseGrep相交
    const IBaseGraphicsElementShapeComponent* pBaseGraphicsElementShapeComponent = pHostInstance->GetBaseGraphicsElementShapeComponent();
    const IGraphicsElementShape* wallBaseGrep = pBaseGraphicsElementShapeComponent->GetBaseGraphicsElementShape();

    // 获取洞口的打洞图形
    OwnerPtr<IGraphicsElementShape> opClonedCutterGRep;
    const IGeometryRelationshipComponent* pGeometryRepCpt = pHole->GetGeometryRelationshipComponent();
    const IOpeningGraphicsElementShapeComponent* pOpeningGRepComponent = pGeometryRepCpt->GetOpeningGraphicsElementShapeComponent();
    const IGraphicsElementShape* pCutterGRep = pOpeningGRepComponent->GetOpeningGraphicsElementShape();

    // 应用变换
    opClonedCutterGRep = TransferOwnershipCast<IGraphicsElementShape>(pCutterGRep->Clone());
    const IElementPosition *posBehavior = pHole->GetElementPosition();
    Matrix4d instanceTrans = posBehavior->ComputeLocalToWorldCoordinateSystemTransformMatrix();
    if (!instanceTrans.IsIdentity())
    {
        opClonedCutterGRep->Transform(instanceTrans);
    }

    // 判断位置关系
    BodiesPositionTypes positionType = GraphicsNodeUtils::GetBodiesPositionType(pHostInstance->GetDocument(), wallBaseGrep, opClonedCutterGRep.get());
    if (positionType != BodiesPositionTypes::Separation)
    {
        return true;
    }

    return false;
}
```

### 墙洞图形表达组件

墙洞的图形表达是通过GDMP的打洞机制实现的，主要涉及`IOpeningGraphicsElementShapeComponent`组件和打洞关系的建立。

墙洞与墙体之间的打洞关系通过`IElementOpening::Create`方法建立：

```cpp
// 打洞关系建立
if (pHostInstance)
{
    IElementOpening::Create(pDoc, pInstance->GetElementId(), pHostInstance->GetElementId());
}
```

这一步骤将墙洞实例注册为墙体的打洞元素，使得墙体在图形表达时会考虑墙洞的存在。

墙洞实例创建时，会自动创建`IOpeningGraphicsElementShapeComponent`组件，用于存储打洞图形：

```cpp
IGeometryRelationshipComponent* pGeometryRepCpt = pInstance->GetGeometryRelationshipComponent();
IOpeningGraphicsElementShapeComponent* pOpeningGRepComponent = pGeometryRepCpt->GetOpeningGraphicsElementShapeComponent();
pOpeningGRepComponent->SetShouldCalculateIntersectingGraphicsElementShape(true);
```

`SetShouldCalculateIntersectingGraphicsElementShape(true)`设置表示需要计算墙洞与墙体的交集图形，这是实现打洞效果的关键。当墙体或墙洞发生变化时，系统会自动重新计算交集图形，更新打洞效果。
