## 屋顶创建命令
屋顶的创建命令通过`CmdCreateStructureRoof`类实现：

```cpp
class CmdCreateStructureRoof :public GbmpCommandBase
{
public:
    CmdCreateStructureRoof();
    virtual ~CmdCreateStructureRoof() {}

public:
    virtual bool IsEnabled() const override;
    virtual bool IsVisible() const override;
    virtual gcmp::OwnerPtr<gcmp::IAction> ExecuteCommand(const gcmp::CommandParameters& cmdParams) override;

    StructureFamilyUiUtils::FamilyCreators GetDefaultFamilyCreators() const;
    std::wstring GetDefaultFamilyName() const;
    UniIdentity GetCategory() const;
    Int32 GetCategorySecondKey()const;
    bool CheckRunTimeEnvironment() const;

    // 命令执行前清空选择集
    virtual bool ShouldClearSelectionBeforeExecution() const override { return true; }
};
```

`CmdCreateStructureRoof`类继承自`GbmpCommandBase`，是屋顶创建命令的入口。该命令的主要职责是初始化屋顶创建环境，并启动屋顶编辑模式。

创建命令的执行流程如下：

1. **检查运行环境**：调用`CheckRunTimeEnvironment`方法检查运行环境，确保当前没有其他编辑模式在运行

   ```cpp
   bool CmdCreateStructureRoof::CheckRunTimeEnvironment() const
   {
       IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
       // 检查是否有顶层编辑模式正在运行
       IEditMode* pTopEditMode = IEditMode::GetTopActiveEditMode(pDoc);
       if (pTopEditMode != nullptr)
       {
           UiCommonDialog::ShowMessageBox(GBMP_TR(L"警告"),
               GBMP_TR(L"正在编辑屋顶，不能重新创建实例！"),
               (int)UiCommonDialog::ButtonType::OK);
           return false;
       }
       return true;
   }
   ```

2. **获取族和类型**：通过`StructureFamilyUiUtils`获取屋顶族和类型，如果不存在则创建内建屋顶族

   ```cpp
   StructureFamilyUiUtils::FamilyCreators defaultFamilyCreators = this->GetDefaultFamilyCreators();
   StructureFamilyUiUtils::CreateFamilies(pDoc, defaultFamilyCreators);
   ```

3. **创建事务组**：创建事务组，用于管理创建过程中的所有操作

   ```cpp
   int groupId;
   UserTransactionGroupUtils::Start(pDoc, GBMP_TR(L"创建屋顶"), groupId);
   ```

4. **创建编辑模式**：创建`StructureRoofEditModeBehavior`编辑模式

   ```cpp
   IEditMode* pProfileEditMode = IEditMode::Get(pDoc, StructureEditModeTypes::StructureRoofProfileModeType);
   if (!pProfileEditMode)
   {
       pProfileEditMode = IEditMode::Create(pDoc, IStructureRoofEditModeBehavior::Create());
   }
   ```

5. **设置编辑模式参数**：设置编辑模式的族ID和类型ID

   ```cpp
   IStructureRoofEditModeBehavior* pProfileEditModeBehavior =
       quick_cast<IStructureRoofEditModeBehavior>(pProfileEditMode->GetEditModeBehavior());
   pProfileEditModeBehavior->SetEditingFamilyId(pFamily->GetElementId());
   pProfileEditModeBehavior->SetEditingInstanceTypeId(pInstanceType->GetElementId());
   ```

6. **启动编辑模式**：调用`StartEditing`方法启动编辑模式，进入屋顶轮廓绘制状态

   ```cpp
   pProfileEditMode->StartEditing(pDoc, ElementId::InvalidID, true, false);
   ```

7. **用户交互**：用户通过交互绘制轮廓线和坡度线，这部分由编辑模式和相关命令处理

8. **完成创建**：用户完成绘制后，调用`CmdFinishDrawRoofProfile`命令完成创建


## 屋顶轮廓绘制命令

屋顶轮廓的绘制通过`CmdDrawRoofStraightProfile`类实现，该类继承自`CmdDrawStraightProfileBase`：

```cpp
class CmdDrawRoofStraightProfile : public CmdDrawStraightProfileBase
{
public:
    CmdDrawRoofStraightProfile(const std::wstring& cmdId = ID_CMD_STRUCTURE_DRAW_ROOF_STRAIGHT_PROFILE)
        : CmdDrawProfileBase(cmdId)
    {}

public:
    virtual OwnerPtr<IAction> ExecuteCommand(const CommandParameters& cmdParams) override;
    ElementId OnLineFinished(IUiView* pCurrentView, const Vector3d& startPt, const Vector3d& endPt,
        const ISnap* pStartSnap, const ISnap* pEndSnap);
};
```

`CmdDrawRoofStraightProfile`类负责处理屋顶轮廓线的绘制，它通过`ActionDrawStraightElementCreator`创建一个直线绘制动作，并在线段绘制完成时创建用于屋顶轮廓的模型线。

绘制轮廓的流程如下：

1. **创建绘制动作**：在`ExecuteCommand`方法中创建`ActionDrawStraightElementInput`对象，并设置回调函数

   ```cpp
   OwnerPtr<IAction> CmdDrawRoofStraightProfile::ExecuteCommand(const CommandParameters& cmdParams)
   {
       // 创建绘制动作输入参数
       ActionDrawStraightElementInput actionInput;
       actionInput.CommandId = GetCommandId();
       actionInput.drawLineMode = DrawLineMode::Chained;
       actionInput.LineFinishedCallback = ActionDrawStraightElementInput::CreateOnLineFinishedCallback(
           &CmdDrawRoofStraightProfile::OnLineFinished, this);

       // 创建绘制动作
       OwnerPtr<IAction> opAction = ActionDrawStraightElementCreator::Create(actionInput);
       return TransferOwnership(opAction);
   }
   ```

2. **用户交互**：用户通过鼠标点击指定轮廓线的起点和终点

3. **创建模型线**：在`OnLineFinished`回调方法中，调用`StructureRoofProfileUsedModelLineUtils::CreateModelLineUsedForRoofProfile`方法创建模型线

   ```cpp
   ElementId CmdDrawRoofStraightProfile::OnLineFinished(IUiView* pCurrentView, const Vector3d& startPt,
       const Vector3d& endPt, const ISnap* pStartSnap, const ISnap* pEndSnap)
   {
       // 创建事务
       OwnerPtr<IUserTransaction> opUt = IUserTransaction::Create(pDoc, GBMP_TR(L"绘制轮廓"));

       // 创建模型线
       IModelLine* pIModelLine = gcmp::StructureRoofProfileUsedModelLineUtils::CreateModelLineUsedForRoofProfile(
           pDoc, ILine3d::Create(startPt, endPt));

       // 设置渲染层
       pIModelLine->SetRenderLayer(GraphicsRenderLayer::ProfileCurve);

       opUt->Commit();
       return pIModelLine->GetOwnerElement()->GetElementId();
   }
   ```

4. **设置渲染层**：将模型线设置为轮廓线渲染层，使其在视图中以特定样式显示

5. **标记轮廓线**：通过`StructureRoofProfileUsedModelLineUtils`工具类，为模型线添加外部数据，标记其为屋顶轮廓线

   ```cpp
   // 在StructureRoofProfileUsedModelLineUtils::CreateModelLineUsedForRoofProfile方法中
   pModelLine->GetExternalDatas()->RegisterExternalData(ED_KEY_MODEL_LINE_USED_ROOF_SLOPE,
       NEW_AS_OWNER_PTR(ModelLineUsedRoofSlopeData, pDoc, pModelLine->GetOwnerElement()->GetElementId()));
   ```

## 屋顶完成绘制命令

完成屋顶轮廓绘制的命令通过`CmdFinishDrawRoofProfile`类实现：

```cpp
class CmdFinishDrawRoofProfile : public GbmpCommandBase
{
public:
    CmdFinishDrawRoofProfile();
    virtual ~CmdFinishDrawRoofProfile() {}

public:
    virtual bool IsEnabled() const override;
    virtual bool IsVisible() const override;
    virtual OwnerPtr<IAction> ExecuteCommand(const CommandParameters& cmdParams) override;

    UniIdentity GetCategory() const;
    Int32 GetCategorySecondKey()const;
};
```

`CmdFinishDrawRoofProfile`类负责完成屋顶轮廓的绘制过程，收集用户绘制的轮廓线和坡度线，并创建屋顶实例。该命令在用户点击编辑模式框中的"完成"按钮时触发。

完成绘制的流程如下：

1. **获取编辑模式**：获取当前的屋顶编辑模式和相关参数

   ```cpp
   const IEditMode* pProfileEditMode = IEditMode::Get(pDoc, StructureEditModeTypes::StructureRoofProfileModeType);
   const IStructureRoofEditModeBehavior* pProfileEditModeBehavior =
       quick_cast<const IStructureRoofEditModeBehavior>(pProfileEditMode->GetEditModeBehavior());

   bool bIsCreating = pProfileEditMode->GetIsCreating();
   ElementId familyId = pProfileEditModeBehavior->GetEditingFamilyId();
   ElementId instanceTypeId = pProfileEditModeBehavior->GetEditingInstanceTypeId();
   ```

2. **收集轮廓线和坡度线**：收集用户绘制的轮廓线和坡度线

   ```cpp
   // 获取当前文档里的轮廓线
   std::vector<IModelLine*> modelLines;
   StructureRoofProfileUsedModelLineUtils::GetRoofProfileModelLinesInCurrentDocument(pDoc, modelLines);

   // 获取当前文档里的坡度线
   std::vector<IStructureSlopeLine*> slopeLines;
   StructureSlopeLineUtils::GetSlopeLinesInCurrentDocument(pDoc, slopeLines);
   ```

3. **创建输入数据**：创建`StructureRoofInput`输入数据，包含轮廓线、坡度线和其他参数

   ```cpp
   OwnerPtr<StructureRoofInput> opInput = NEW_AS_OWNER_PTR(StructureRoofInput);
   bool bOk = StructureInstanceCmdUtils::GenerateStructureRoofInput(
       pDoc, pModelView, GetCategory(), GetCategorySecondKey(), familyId, instanceTypeId, *opInput);

   opInput->ProfileCurves = profileCurves;
   opInput->SlopeLines = slopeLines;
   ```

4. **退出编辑模式**：调用`EndEditing`方法退出编辑模式

   ```cpp
   IEditMode* pEditMode = IEditMode::Get(pDoc, StructureEditModeTypes::StructureRoofProfileModeType);
   pEditMode->EndEditing(pDoc);
   ```

5. **创建屋顶实例**：调用`IStructureRoof::Create`方法创建屋顶实例

   ```cpp
   IStructureRoof* pRoof = IStructureRoof::Create(opInput.get());
   if (!pRoof)
   {
       UiCommonDialog::ShowMessageBox(GBMP_TR(L"警告"),
           GBMP_TR(L"生成形体失败，请重新编辑屋顶轮廓！"),
           (int)UiCommonDialog::ButtonType::OK);
       opTransaction->Rollback();
       return nullptr;
   }
   ```

6. **清空选择集**：清空选择集和高亮显示

   ```cpp
   ISelection::Get()->Clear(pDoc);
   IHighlights::Get()->Clear();
   ```

## 屋顶编辑模式行为

屋顶的编辑模式通过`StructureRoofEditModeBehavior`类实现，该类继承自`NdbObjectSchematic`和`IStructureRoofEditModeBehavior`接口：

```cpp
class StructureRoofEditModeBehavior : public gcmp::NdbObjectSchematic, public IStructureRoofEditModeBehavior
{
    DBOBJECT_DATA_BEGIN_AND_QUICK_CAST_FROM(gcmp, StructureRoofEditModeBehavior, gcmp::NdbObjectSchematic, 6224601B-D139-4E15-A181-214E8B560B9F, gmstructure, gcmp::IStructureRoofEditModeBehavior)
        DATA(ElementId, EditingFamilyId)
        DATA(ElementId, EditingInstanceTypeId)
        DATA(ElementId, PresetElementId)
    DATA_END

    // 方法实现...
};
```

`StructureRoofEditModeBehavior`类是屋顶编辑模式的核心实现，它通过NDB数据存储系统存储编辑状态，并提供编辑模式的行为实现。

`IStructureRoofEditModeBehavior`接口继承自`IStructureEditModeBehavior`，后者又继承自`IEditModeBehavior`。这种继承关系使得屋顶编辑模式能够利用结构元素编辑模式的通用功能，同时添加屋顶特有的编辑行为。

编辑模式行为的主要数据成员包括：

- **EditingFamilyId**：正在编辑的族ID，用于标识屋顶所属的族
- **EditingInstanceTypeId**：正在编辑的实例类型ID，用于标识屋顶的类型
- **PresetElementId**：预设元素ID，用于存储临时预设元素的ID

屋顶的轮廓编辑通过编辑模式实现，主要流程包括：

1. **进入编辑模式**：调用`StartEditing`方法进入编辑模式

   ```cpp
   bool StructureRoofEditModeBehavior::StartEditing(gcmp::IDocument* pDoc, bool isCreating)
   {
       IEditMode* pProfileEditMode = IEditMode::Get(pDoc, GetEditModeType());
       pProfileEditMode->SetIsCreating(isCreating);
       return true;
   }
   ```

   `StartEditing`方法获取屋顶编辑模式，并设置是否为创建模式。在创建模式下，完成编辑后会创建新的屋顶实例；在编辑模式下，完成编辑后会更新现有的屋顶实例。

2. **编辑轮廓**：通过命令交互编辑轮廓线和坡度线，包括添加、修改和删除轮廓线和坡度线

   在编辑过程中，用户可以使用`CmdDrawRoofStraightProfile`等命令绘制轮廓线，使用`CmdDrawSlopeline`命令绘制坡度线。这些命令会在文档中创建临时的模型线和坡度线，并通过外部数据标记它们的用途。

3. **退出编辑模式**：调用`EndEditing`方法退出编辑模式，应用编辑结果

   ```cpp
   bool StructureRoofEditModeBehavior::EndEditing(gcmp::IDocument* pDoc)
   {
       // 删除预设元素
       this->DeletePresetElementId(pDoc);
       return true;
   }
   ```

   `EndEditing`方法主要负责清理编辑模式中创建的临时对象，如预设元素。实际的屋顶创建或更新操作由`CmdFinishDrawRoofProfile`命令完成。

编辑模式行为还提供了其他重要方法：

- **GetEditModeType**：返回编辑模式类型，用于标识屋顶编辑模式

   ```cpp
   std::wstring StructureRoofEditModeBehavior::GetEditModeType() const
   {
       return StructureEditModeTypes::StructureRoofProfileModeType;
   }
   ```

- **IsElementEditable**：判断元素是否可在编辑模式中编辑

   ```cpp
   bool StructureRoofEditModeBehavior::IsElementEditable(const IElement* pElement) const
   {
       // 轮廓线可以编辑
       if (StructureRoofProfileUsedModelLineUtils::IsModelLineUsedForRoofProfile(pElement))
       {
           return true;
       }

       // 坡度线可以编辑
       if (const IGenericElement* pGenericElement = quick_cast<IGenericElement>(pElement))
       {
           if (const IStructureSlopeLine* pSlope = quick_cast<IStructureSlopeLine>(pGenericElement->GetExternalObject()))
           {
               return true;
           }
       }

       return false;
   }
   ```

- **ReportParents**：报告编辑模式的父元素关系，用于处理元素依赖关系
- **UpdateForWeakParentDeletion**：处理弱依赖父元素被删除的情况，确保编辑模式的稳定性

屋顶编辑模式的UI交互通过`UIRoofProfileEditModeBehavior`类实现，该类继承自`UIProfileSlopeEditModeBehaviorBase`，提供了编辑模式下的UI交互功能，如显示编辑模式框、设置上下文菜单等。

## 屋顶编辑命令实现

屋顶的编辑命令通过`CmdEditRoofProfile`类实现：

```cpp
class CmdEditRoofProfile : public CmdEditDrawProfileBase
{
public:
    CmdEditRoofProfile(const std::wstring& cmdId = ID_CMD_EDIT_STRUCTURE_ROOF);
    virtual ~CmdEditRoofProfile();

public:
    virtual OwnerPtr<IAction> ExecuteCommand(const CommandParameters& cmdParams) override;
    virtual UniIdentity GetCategory() const override;

private:
    void GetProfileCurvesAndSlopeLines(ElementId instanceId, std::vector<IElement*>& profileCurveElements,
        std::vector<IElement*>& slopeLineElements) const;
};
```

`CmdEditRoofProfile`类继承自`CmdEditDrawProfileBase`，负责处理屋顶的编辑操作。当用户选中一个屋顶实例并点击编辑按钮时，该命令会被触发，进入屋顶编辑模式。

编辑命令的执行流程如下：

1. **获取选中的屋顶实例**：从选择集中获取屋顶实例

   ```cpp
   ISelection* pSelection = ISelection::Get();
   const std::vector<ElementId>& selectedIds = pSelection->GetSelectedElementIds(pDoc);

   ElementId instanceId = selectedIds[0];
   IInstance* pInstance = quick_cast<IInstance>(pDoc->GetElement(instanceId));
   ```

2. **获取轮廓线和坡度线**：调用`GetProfileCurvesAndSlopeLines`方法获取轮廓线和坡度线

   ```cpp
   std::vector<IElement*> profileCurveElements;
   std::vector<IElement*> slopeLineElements;
   GetProfileCurvesAndSlopeLines(instanceId, profileCurveElements, slopeLineElements);
   ```

   `GetProfileCurvesAndSlopeLines`方法从屋顶实例中获取轮廓线和坡度线的ElementId，然后从文档中获取对应的元素：

   ```cpp
   void CmdEditRoofProfile::GetProfileCurvesAndSlopeLines(ElementId instanceId,
       std::vector<IElement*>& profileCurveElements, std::vector<IElement*>& slopeLineElements) const
   {
       IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
       const IStructureRoof* pRoof = IStructureRoof::Get(quick_cast<IInstance>(pDoc->GetElement(instanceId)));

       // 获取轮廓线
       const std::vector<ElementId>& profileCurveIds = pRoof->GetProfileCurveIds();
       FOR_EACH(id, profileCurveIds)
       {
           IElement* pElement = pDoc->GetElement(id);
           if (StructureRoofProfileUsedModelLineUtils::IsModelLineUsedForRoofProfile(pElement))
           {
               profileCurveElements.push_back(pElement);
           }
       }

       // 获取坡度线
       const std::vector<ElementId>& slopeLineIds = pRoof->GetSlopeLineIds();
       FOR_EACH(id, slopeLineIds)
       {
           IElement* pElement = pDoc->GetElement(id);
           slopeLineElements.push_back(pElement);
       }
   }
   ```

3. **创建事务组**：创建事务组，用于管理编辑过程中的所有操作

   ```cpp
   int groupId;
   UserTransactionGroupUtils::Start(pDoc, GBMP_TR(L"编辑屋顶"), groupId);
   ```

4. **创建编辑模式**：创建`StructureRoofEditModeBehavior`编辑模式

   ```cpp
   IEditMode* pProfileEditMode = IEditMode::Get(pDoc, StructureEditModeTypes::StructureRoofProfileModeType);
   if (!pProfileEditMode)
   {
       pProfileEditMode = IEditMode::Create(pDoc, IStructureRoofEditModeBehavior::Create());
   }
   ```

5. **设置编辑模式参数**：设置编辑模式的族ID、类型ID和实例ID

   ```cpp
   IStructureRoofEditModeBehavior* pProfileEditModeBehavior =
       quick_cast<IStructureRoofEditModeBehavior>(pProfileEditMode->GetEditModeBehavior());
   pProfileEditModeBehavior->SetEditingFamilyId(pInstance->GetFamilyId());
   pProfileEditModeBehavior->SetEditingInstanceTypeId(pInstance->GetInstanceTypeId());
   ```

6. **启动编辑模式**：调用`StartEditing`方法启动编辑模式

   ```cpp
   pProfileEditMode->StartEditing(pDoc, instanceId, false, false);
   ```

7. **隐藏原实例**：将原实例设置为不可见，以便用户可以清晰地看到和编辑轮廓线和坡度线

   ```cpp
   pInstance->GetStatus()->SetVisible(false);
   ```

8. **用户交互**：用户通过交互编辑轮廓线和坡度线，这部分由编辑模式和相关命令处理

9. **完成编辑**：用户完成编辑后，调用`CmdFinishDrawRoofProfile`命令完成编辑，更新屋顶实例
