# 屋顶创建过程

## 屋顶创建流程
屋顶实例的创建主要通过`IStructureRoof::Create`方法实现，该方法调用了`GmIStructureFactory::CreateStructureRoof`方法：

```cpp
IStructureRoof* IStructureRoof::Create(const StructureRoofInput* pInput)
{
    return GmIStructureFactory::Get()->CreateStructureRoof(pInput);
}
```

实际的创建过程在`StructureRoof`类的静态方法中实现：

```cpp
IStructureRoof* StructureRoof::Create(const StructureRoofInput* pInput)
{
    // 参数检查
    IDocument* pDoc = pInput->Document;
    IInstanceType* pInstanceType = quick_cast<IInstanceType>(pDoc->GetElement(pInput->InstanceTypeId));

    // 创建实例
    IInstance* pInstance = pInstanceType->CreateInstance(L"屋顶实例", pInput->CanBeShared, pInput->CreationOption, pInput->IsCreateGeometryRelationshipComponent);
    ElementId instanceId = pInstance->GetElementId();

    // 注册外部数据
    StructureRoof* pRoof = StructureRoof::RegisterExternalData(pInstance);
    pRoof->SetConcreteStrengthGrade(pInput->ConcreteType);

    // 轮廓信息
    std::vector<IModelLine*> profileCurves = pInput->ProfileCurves;
    std::vector<IStructureSlopeLine*> slopeLines = pInput->SlopeLines;

    // 更新轮廓信息
    bool bOk = pRoof->UpdateProfileCurvesAndSlopeLines(profileCurves, slopeLines);

    // 为模型线绑定实例对象
    StructureRoofProfileUsedModelLineUtils::SetAssociatedInstanceIdForModelLines(profileCurves, instanceId);

    // 将模型线和坡度线从世界坐标系转换到屋顶的局部坐标系
    const IElementPosition *posBehavior = pInstance->GetElementPosition();
    Matrix4d mat = posBehavior->ComputeLocalToWorldCoordinateSystemTransformMatrix();
    bOk = mat.MakeInverse();
    bOk = pRoof->TransformSlopeLinesAndProfileCurvesCoordinateSystem(profileCurves, slopeLines, mat, instanceId, pInput->AssociatedPlaneOffset, true);

    // 建立坡度线到轮廓线的映射关系
    std::map<IStructureSlopeLine*, IModelLine*> slopeLineAndProfileCurveMap;
    if (slopeLines.size() > 0)
    {
        bOk = pRoof->GenerateSlopeLineAndProfileCurveMap(slopeLines, profileCurves, slopeLineAndProfileCurveMap);
    }

    // 生成屋顶自定义数据
    OwnerPtr<RoofCustomData> opRoofData = NEW_AS_OWNER_PTR(RoofCustomData);
    bOk = pRoof->GenerateRoofCustomData(opRoofData, profileCurves, slopeLineAndProfileCurveMap);
    opRoofData->SetOwnerElement(pInstance);

    // 设置自定义参数
    IElementParameters* pElementParam = pInstance->GetElementParameters();
    OwnerPtr<IParameter> opParam = pElementParam->GetParameterByUid(PARAMETER_UID(InputCustomDataBuiltInParameter));
    opParam->SetValueAsCustomData(TransferOwnership(opRoofData));
    pElementParam->SetParameter(opParam.get());

    // 重新生成元素
    bOk = pDoc->GetRegenerator()->RegenerateElement(instanceId);

    return pRoof;
}
```

屋顶的几何形状由自定义数据参数（`InputCustomDataBuiltInParameter`）驱动，当自定义数据参数发生变化时，会触发屋顶的重新生成。上述代码中通过调用`RegenerateElement`方法触发屋顶实例的重新生成。

### IInstance与StructureRoof的关联
屋顶实例与外部数据的关联通过`RegisterExternalData`方法实现：

```cpp
StructureRoof* StructureRoof::RegisterExternalData(IInstance* pInstance)
{

    IExternalDataComponent* pExternalDataComponent = pInstance->GetExternalDataComponent();

    StructureRoof* pRoof = quick_cast<StructureRoof>(pExternalDataComponent->FindExternalData(StructureRoof::GetStaticClassSchema()->GetName()));
    if (pRoof)
    {
        return pRoof;
    }

    pRoof = NEW_AS_OWNER_PTR(StructureRoof);
    pRoof->SetOwnerElement(pInstance);
    pExternalDataComponent->AddExternalData(TransferOwnership(pRoof));

    return pRoof;
}
```

这个方法将`StructureRoof`对象注册为实例的外部数据，建立了实例与外部数据的关联。

### 轮廓信息更新机制
屋顶创建过程中的一个重要步骤是更新轮廓信息，通过`UpdateProfileCurvesAndSlopeLines`方法实现：

```cpp
bool StructureRoof::UpdateProfileCurvesAndSlopeLines(const std::vector<IModelLine*> profileCurves, const std::vector<IStructureSlopeLine*> slopeLines)
{
    // 存储轮廓线的ElementId
    std::vector<ElementId> profileCurveIds;
    FOR_EACH(pModelLine, profileCurves)
    {
        profileCurveIds.push_back(pModelLine->GetOwnerElement()->GetElementId());
    }
    this->SetProfileCurveIds(profileCurveIds);

    // 存储坡度线的ElementId
    std::vector<ElementId> slopeLineIds;
    FOR_EACH(pSlopeLine, slopeLines)
    {
        slopeLineIds.push_back(pSlopeLine->GetOwnerElement()->GetElementId());
    }
    this->SetSlopeLineIds(slopeLineIds);

    return true;
}
```

这个方法的主要作用是将轮廓线和坡度线的ElementId存储到屋顶对象中，以便后续可以通过这些ID找到对应的轮廓线和坡度线。具体来说：

1. **存储轮廓线ID**：遍历轮廓线集合，获取每个轮廓线的ElementId，并存储到屋顶对象的`ProfileCurveIds`属性中
2. **存储坡度线ID**：遍历坡度线集合，获取每个坡度线的ElementId，并存储到屋顶对象的`SlopeLineIds`属性中

这种存储方式使得屋顶对象可以在后续操作中（如编辑、更新等）找到相关的轮廓线和坡度线，而不需要直接持有这些对象的指针。

### 轮廓线关联机制
屋顶创建过程中的另一个重要步骤是建立轮廓线与屋顶实例的关联，通过`SetAssociatedInstanceIdForModelLines`方法实现：

```cpp
bool StructureRoofProfileUsedModelLineUtils::SetAssociatedInstanceIdForModelLines(std::vector<IModelLine*> profileCurves, ElementId instanceId)
{
    FOR_EACH(pModelLine, profileCurves)
    {
        if (IsModelLineUsedForRoofProfile(pModelLine))
        {
            // 获取轮廓线的扩展数据
            auto pData = pModelLine->GetExternalDatas()->FindExternalData(ED_KEY_MODEL_LINE_USED_ROOF_SLOPE);

            // 转换为ModelLineUsedRoofSlopeData类型
            ModelLineUsedRoofSlopeData* pProfileData = quick_cast<ModelLineUsedRoofSlopeData>(pData);

            // 设置关联的实例ID
            pProfileData->SetInstanceId(instanceId);
        }
        else
        {
            DBG_WARN(L"当前模型线不是用于绘制屋顶轮廓的，请检查轮廓信息",L"GDMPLab",L"2024-03-30");
        }
    }

    return true;
}
```

这个方法的主要作用是将屋顶实例的ID设置到轮廓线的扩展数据中，建立轮廓线与屋顶实例的关联。具体来说：

1. **获取轮廓线的扩展数据**：通过`IExternalDatas::FindExternalData`方法获取轮廓线的`ModelLineUsedRoofSlopeData`扩展数据
2. **设置实例ID**：通过`ModelLineUsedRoofSlopeData::SetInstanceId`方法将屋顶实例的ID设置到轮廓线的扩展数据中

这种关联机制使得系统可以通过轮廓线找到对应的屋顶实例，例如在编辑轮廓线时，可以自动更新关联的屋顶实例。同时，`ModelLineUsedRoofSlopeData`类还存储了轮廓线的其他属性，如是否定义坡度、坡度角度、轮廓线偏移等，这些属性在生成屋顶几何形体时会被使用。

### 屋顶和项目坐标系转换
屋顶创建过程中的一个关键步骤是坐标系转换，通过`TransformSlopeLinesAndProfileCurvesCoordinateSystem`方法实现：

```cpp
bool StructureRoof::TransformSlopeLinesAndProfileCurvesCoordinateSystem(
    std::vector<IModelLine*> profileCurves,
    std::vector<IStructureSlopeLine*> slopeLines,
    const Matrix4d& transformMatrix,
    const ElementId instanceId,
    const double associatedPlaneOffset,
    bool isCreating)
{
    // 参数检查

    // 对每个轮廓线和坡度线应用变换矩阵
    FOR_EACH(pModelLine, profileCurves)
    {
        IElementTransformationComponent* pTransformationComponent = pModelLine->GetOwnerElement()->GetElementTransformationComponent();
        pTransformationComponent->Transform(transformMatrix);
    }

    FOR_EACH(pSlopeLine, slopeLines)
    {
        IElementTransformationComponent* pTransformationComponent = pSlopeLine->GetOwnerElement()->GetElementTransformationComponent();
        pTransformationComponent->Transform(transformMatrix);
    }

    return true;
}
```

坐标系转换的主要目的是将轮廓线和坡度线从世界坐标系转换到屋顶的局部坐标系，这样在生成屋顶几何形体时，可以正确地处理轮廓线和坡度线的位置关系。转换过程包括：

1. **获取变换矩阵**：通过`IElementPosition::ComputeLocalToWorldCoordinateSystemTransformMatrix`获取局部到世界的变换矩阵，然后求逆得到世界到局部的变换矩阵
2. **应用变换矩阵**：对每个轮廓线和坡度线应用变换矩阵，将它们从世界坐标系转换到屋顶的局部坐标系
3. **设置可见性**：根据需要设置轮廓线和坡度线的可见性

### 建立坡度线到轮廓线的映射关系
对于有坡度线的屋顶，需要建立坡度线到轮廓线的映射关系，通过`GenerateSlopeLineAndProfileCurveMap`方法实现：

```cpp
bool StructureRoof::GenerateSlopeLineAndProfileCurveMap(
    std::vector<IStructureSlopeLine*>& slopeLines,
    std::vector<IModelLine*>& profileCurves,
    std::map<IStructureSlopeLine*, IModelLine*>& slopeLineAndProfileCurveMap)
{
    // 记录原始数量
    int count = slopeLines.size() + profileCurves.size();

    for (int i = 0; i < slopeLines.size(); i++)
    {
        // 判断坡度线的起点是否在某轮廓线上
        bool isSlopeLineStartPointOnProfileCurve = false;
        Vector3d startPt = slopeLines[i]->GetStart();

        for (int j = 0; j < profileCurves.size(); j++)
        {
            // 计算坡度线起点到轮廓线的最小距离
            Vector3d minimalDistancePointOnCurve;
            double pParamOnCurve;
            double minimalDistance;
            AlgorithmMinimalDistance::Calculate(profileCurves[j]->GetGeometryCurve().get(),
                startPt, minimalDistancePointOnCurve, true, &pParamOnCurve, minimalDistance);

            // 如果距离小于容差，认为坡度线起点在轮廓线上
            if (minimalDistance + Constants::DOUBLE_EPS < gcmp::Constants::LENGTH_EPS)
            {
                // 建立坡度线和轮廓线的映射关系
                slopeLineAndProfileCurveMap.insert(std::pair<IStructureSlopeLine*, IModelLine*>(
                    slopeLines[i], profileCurves[j]));
                // 从轮廓线集合中移除已配对的轮廓线
                profileCurves.erase(profileCurves.begin() + j);
                isSlopeLineStartPointOnProfileCurve = true;
                break;
            }
        }

        // 检查坡度线位置是否正确
        if (!isSlopeLineStartPointOnProfileCurve)
        {
            DBG_WARN(L"坡度线位置不正确：坡度线的起点必须在轮廓线上！",L"GDMPLab",L"2024-03-30");
        }
    }

    return true;
}
```

建立坡度线和轮廓线的映射关系，确定哪个坡度线对应哪个轮廓线。这种映射关系在生成屋顶自定义数据时非常重要，因为它决定了屋顶各个面的坡度。分组过程包括：

1. **遍历坡度线**：对每个坡度线，获取其起点
2. **查找对应轮廓线**：计算坡度线起点到各轮廓线的最小距离，找到距离最小且小于容差的轮廓线
3. **建立映射关系**：将找到的坡度线和轮廓线建立映射关系，并从轮廓线集合中移除已配对的轮廓线

### 自定义参数生成机制
屋顶创建过程中的一个关键步骤是生成自定义参数`RoofCustomData`，`RoofCustomData`是GDMP SDK专门提供的参数，可以传递给族服务用于创建和更新屋顶几何形体。屋顶创建中通过`GenerateRoofCustomData`方法实现：

```cpp
bool StructureRoof::GenerateRoofCustomData(
    OwnerPtr<RoofCustomData>& opRoofData,
    std::vector<IModelLine*>& profileCurves,
    std::map<IStructureSlopeLine*, IModelLine*>& slopeLineAndProfileCurveMap)
{
    // 创建投影平面
    OwnerPtr<IPlane> opPlane = IPlane::Create(Vector3d::Zero, Vector3d::UnitX, Vector3d::UnitY);

    // 处理有坡度线的轮廓线
    for (auto it = slopeLineAndProfileCurveMap.begin(); it != slopeLineAndProfileCurveMap.end(); it++)
    {
        IStructureSlopeLine* pSlopeLine = it->first;
        IModelLine* pModelLine = it->second;

        // 设置SlopeLineData
        OwnerPtr<SlopeLineData> opSlopeLineData = NEW_AS_OWNER_PTR(SlopeLineData);
        OwnerPtr<ICurve2d> opSlopeCurve = AlgorithmProject::Project(pSlopeLine->GetSlopeLine().get(), opPlane.get());
        opSlopeLineData->SetSlopeLine(TransferOwnership(opSlopeCurve));
        double slopeAngle = pSlopeLine->GetSlopeAngle();  // 角度为弧度制
        opSlopeLineData->SetSlopeAngle(slopeAngle);

        // 设置ProfileCurveData
        OwnerPtr<ProfileCurveData> opProfileCurveData = NEW_AS_OWNER_PTR(ProfileCurveData);
        OwnerPtr<ICurve2d> opProfileCurve = AlgorithmProject::Project(pModelLine->GetGeometryCurve().get(), opPlane.get());
        opProfileCurveData->SetProfileCurve(TransferOwnership(opProfileCurve));
        opProfileCurveData->SetIsDefineSlope(false);      // 定义了坡度线，则轮廓线就不会定义坡度
        opProfileCurveData->SetSlopeLineData(TransferOwnership(opSlopeLineData));

        opRoofData->AddProfileCurveData(TransferOwnership(opProfileCurveData));

        // 设置轮廓线和坡度线不可见
        pModelLine->GetOwnerElement()->GetStatus()->SetIsVisible(false);
        pSlopeLine->GetOwnerElement()->GetStatus()->SetIsVisible(false);
    }

    // 处理没有坡度线的轮廓线
    FOR_EACH(pModelLine, profileCurves)
    {
        // 设置ProfileCurveData
        OwnerPtr<ProfileCurveData> opProfileCurveData = NEW_AS_OWNER_PTR(ProfileCurveData);
        OwnerPtr<ICurve2d> opProfileCurve = AlgorithmProject::Project(pModelLine->GetGeometryCurve().get(), opPlane.get());
        opProfileCurveData->SetProfileCurve(TransferOwnership(opProfileCurve));

        // 获取轮廓线偏移参数
        IElementParameters* pElementParameters = pModelLine->GetOwnerElement()->GetElementParameters();
        OwnerPtr<IParameter> opParamCurveOffset = pElementParameters->GetParameterByUid(PARAMETER_UID(RoofProfileCurveOffsetBuiltInParameter));
        double curveOffset = opParamCurveOffset->GetValueAsDouble();
        opProfileCurveData->SetProfileCurveOffset(curveOffset);

        opProfileCurveData->SetSlopeLineData(nullptr);     // 此分组中的轮廓线没有定义坡度线

        opRoofData->AddProfileCurveData(TransferOwnership(opProfileCurveData));

        // 设置轮廓线不可见
        pModelLine->GetOwnerElement()->GetStatus()->SetIsVisible(false);
    }

    return true;
}
```

自定义数据生成的主要目的是将轮廓线和坡度线的几何信息转换为屋顶自定义数据，这些数据将用于生成屋顶的几何形体。生成过程包括：

1. **创建投影平面**：创建一个XY平面，用于将三维曲线投影为二维曲线
2. **处理有坡度线的轮廓线**：对于每对坡度线和轮廓线，创建对应的`SlopeLineData`和`ProfileCurveData`，并设置相关参数
3. **处理没有坡度线的轮廓线**：对于没有对应坡度线的轮廓线，创建对应的`ProfileCurveData`，并设置相关参数
4. **设置可见性**：将轮廓线和坡度线设置为不可见，因为它们只是用于定义屋顶形状的辅助元素

### 自定义数据参数的应用
自定义数据生成后，需要将其设置到屋顶实例的参数中，通过自定义数据参数（`InputCustomDataBuiltInParameter`）存储和更新：

```cpp
// 设置自定义参数
IElementParameters* pElementParam = pInstance->GetElementParameters();
OwnerPtr<IParameter> opParam = pElementParam->GetParameterByUid(PARAMETER_UID(InputCustomDataBuiltInParameter));
opParam->SetValueAsCustomData(TransferOwnership(opRoofData));
bOk = pElementParam->SetParameter(opParam.get());
```

屋顶的几何形状由自定义数据参数驱动，当自定义数据参数发生变化时，会触发屋顶的重新生成。例如通过调用`RegenerateElement`等更新方法触发屋顶实例的重新生成：

```cpp
bOk = pDoc->GetRegenerator()->RegenerateElement(instanceId);
```

这种参数驱动的机制使得屋顶能够根据轮廓线和坡度线的变化自动更新几何形状，提供了灵活的屋顶设计能力。

## 屋顶数据更新机制

屋顶的数据更新是通过 `StructureRoof::Edit` 方法实现的，该方法接收 `StructureRoofEditInput` 作为输入，更新屋顶的轮廓线和坡度线，并重新生成屋顶的几何形体。

`StructureRoof::Edit`中主要步骤和`StructureRoof::Create`基本一致，包括：
1. 获取输入参数，包括文档、实例ID和实例对象
2. 获取屋顶外部数据
3. 获取新的轮廓线和坡度线
4. 调用 `UpdateProfileCurvesAndSlopeLines` 方法更新轮廓信息
5. 进行坐标转换，将轮廓线和坡度线从世界坐标系转换到屋顶的局部坐标系
6. 进行数据分组，建立坡度线和轮廓线的对应关系
7. 生成屋顶自定义数据
8. 设置自定义参数
9. 调用 `RegenerateElement` 方法重新生成屋顶几何形体

`StructureRoof::Edit`的核心内容是生成`RoofCustomData`参数数据并设置给IInstance，之后调用 `RegenerateElement` 方法触发族服务更新，重新生成屋顶几何形体。

```cpp
    // 设置RoofCustomData参数
    IElementParameters* pElementParam = pInstance->GetElementParameters();
    OwnerPtr<IParameter> opParam = pElementParam->GetParameterByUid(PARAMETER_UID(InputCustomDataBuiltInParameter));
    opParam->SetValueAsCustomData(TransferOwnership(opRoofData));
    pElementParam->SetParameter(opParam.get());
```