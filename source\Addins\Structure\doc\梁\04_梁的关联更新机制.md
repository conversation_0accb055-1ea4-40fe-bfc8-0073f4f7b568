## 梁的父子关系管理机制

梁图元在GDMP中实现了父子关系管理机制，通过`ReportParents`和`UpdateForWeakParentDeletion`方法来管理与其他图元的依赖关系。

### Strong Parent vs Weak Parent概念

GDMP中的图元依赖关系分为两种类型：

1. **Strong Parent（强父图元）**：当强父图元被删除时，当前图元也会被系统自动删除
2. **Weak Parent（弱父图元）**：当弱父图元被删除时，当前图元会收到通知，可以进行相应的处理而不被删除

### 梁图元的父子关系实现

#### StructureBeam基类实现

`StructureBeam`作为梁图元的基础数据类，提供了父子关系管理的基本实现：

<augment_code_snippet path="source/Addins/Structure/StructureModel/Beam/StructureBeam.cpp" mode="EXCERPT">
````cpp
void StructureBeam::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    return;
}

void StructureBeam::ReportParents(IElementParentReporter& reporter) const
{
}
````
</augment_code_snippet>

基类`StructureBeam`的实现为空，表明梁图元本身不直接依赖其他图元。

#### StructureBasicBeam实现

基本梁通过委托模式将父子关系管理委托给基础数据：

<augment_code_snippet path="source/Addins/Structure/StructureModel/Beam/StructureBasicBeam.cpp" mode="EXCERPT">
````cpp
void StructureBasicBeam::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    GetBaseDataFW__()->UpdateForWeakParentDeletion(deletedElementIds);
}

void StructureBasicBeam::ReportParents(IElementParentReporter& reporter) const
{
    GetBaseData__()->ReportParents(reporter);
}
````
</augment_code_snippet>

#### StructureVariableSectionBeam实现

变截面梁同样采用委托模式：

<augment_code_snippet path="source/Addins/Structure/StructureModel/Beam/StructureVariableSectionBeam.cpp" mode="EXCERPT">
````cpp
void StructureVariableSectionBeam::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    GetBaseDataFW__()->UpdateForWeakParentDeletion(deletedElementIds);
}

void StructureVariableSectionBeam::ReportParents(IElementParentReporter& reporter) const
{
    GetBaseData__()->ReportParents(reporter);
}
````
</augment_code_snippet>

### 父子关系管理的调用流程

1. **依赖关系报告**：系统调用`ReportParents`方法收集图元的依赖关系
2. **弱父图元删除处理**：当弱父图元被删除时，系统调用`UpdateForWeakParentDeletion`方法通知相关图元
3. **委托处理**：梁的具体实现类将处理委托给基础数据类`StructureBeam`

### 设计特点

梁图元的父子关系管理具有以下特点：

1. **简洁性**：梁图元本身不直接依赖其他图元，因此实现较为简洁
2. **委托模式**：具体实现类通过委托模式统一处理，保持代码一致性
3. **扩展性**：如果将来需要添加依赖关系，可以在基类中统一实现

梁在GDMP中是一种重要的结构构件，当梁的属性（如位置、尺寸等）发生变化时，需要自动更新其他相关属性（如体积、长度、高程等），以保持模型的一致性。这种关联更新机制是通过GDMP的计算器系统实现的。

GDMP中的计算器是实现关联更新的核心机制。梁相关的计算器主要包括：

1. **基础数据计算器**：更新梁的基本属性，如体积、长度等
2. **矩形梁计算器**：更新矩形梁的特有属性，如角度、高程等
3. **变截面梁计算器**：更新变截面梁的特有属性，如底部角度等

## 基础数据计算器

基础数据计算器是通过`StructureBeam`类的`GetCalculators`方法注册的：

```cpp
void StructureBeam::GetCalculators(ICalculatorCollection * calculators) const
{
    ADD_CALCULATOR(InstanceVolumeCalculator, GetDocument(), GetVolumeRdId());
    ADD_CALCULATOR(StructureInstanceLengthCalculator, GetDocument(), GetLengthRdId());
}
```

### 体积计算器

`InstanceVolumeCalculator`用于计算梁的体积：

```cpp
class InstanceVolumeCalculator : public GbmpCalculatorBase
{
    DECLARE_CALCULATOR(InstanceVolumeCalculator, IInstance)
public:
    InstanceVolumeCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}

    virtual void ReportInputDataIds(std::vector<RegenDataId> & dataIds) const override
    {
        const IInstance* pInstance = GetElement<IInstance>();
        const IElementModelShape* pGRepBehavior = pInstance->GetElementModelShape();

        dataIds.push_back(pGRepBehavior->GetGraphicsElementShapeRdId());
    }

    virtual void Execute() override
    {
        IInstance* pInstance = GetElement<IInstance>();

        double volume = StructureInstanceUtils::GetInstanceVolume(pInstance);
        UniIdentity paramUid = PARAMETER_UID(VolumeBuiltInParameter);
        StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, volume, paramUid);
    }
};
```

该计算器依赖梁的图形表达（GraphicsElementShape）的RegenDataId。当梁的图形表达发生变化时，会触发该计算器执行，重新计算梁的体积并更新到参数中。

### 长度计算器

`StructureInstanceLengthCalculator`用于计算梁的长度：

```cpp
class StructureInstanceLengthCalculator : public GbmpCalculatorBase
{
    DECLARE_CALCULATOR(StructureInstanceLengthCalculator, IInstance)
public:
    StructureInstanceLengthCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}

    virtual void ReportInputDataIds(std::vector<RegenDataId> & dataIds) const override
    {
        const IInstance* pInstance = GetElement<IInstance>();
        const IElementModelShape* pGRepBehavior = pInstance->GetElementModelShape();

        dataIds.push_back(pGRepBehavior->GetGraphicsElementShapeRdId());
    }

    virtual void Execute() override
    {
        IInstance* pInstance = GetElement<IInstance>();

        double length = StructureInstanceUtils::GetInstanceLength(pInstance);
        UniIdentity paramUid = PARAMETER_UID(LengthBuiltInParameter);
        StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, length, paramUid);
    }
};
```

该计算器依赖梁的图形表达的RegenDataId。当梁的图形表达发生变化时，会触发该计算器执行，重新计算梁的长度并更新到参数中。

## 矩形梁计算器

矩形梁计算器是通过`StructureBasicBeam`类的`GetCalculators`方法注册的：

```cpp
void StructureBasicBeam::GetCalculators(ICalculatorCollection * calculators) const
{
    // 首先注册基础数据的计算器
    GetBaseData__()->GetCalculators(calculators);

    // 注册自身的计算器
    ADD_CALCULATOR(StructureBasicBeamAngleCalculator, GetDocument(), GetAngleRdId());

    if (StructureInstanceSectionShapeType::SECTION_SHAPE_RECTANGLE == GetSectionShapeType())
    {
        ADD_CALCULATOR(StructureBasicBeamTopElevationCalculator, GetDocument(), GetBaseData__()->GetTopElevationRdId());
        ADD_CALCULATOR(StructureBasicBeamBottomElevationCalculator, GetDocument(), GetBottomElevationRdId());
        ADD_CALCULATOR(StructureBasicBeamStartTopElevationCalculator, GetDocument(), GetStartTopElevationRdId());
        ADD_CALCULATOR(StructureBasicBeamStartBottomElevationCalculator, GetDocument(), GetBaseData__()->GetStartBottomElevationRdId());
        ADD_CALCULATOR(StructureBasicBeamEndTopElevationCalculator, GetDocument(), GetEndTopElevationRdId());
        ADD_CALCULATOR(StructureBasicBeamEndBottomElevationCalculator, GetDocument(), GetBaseData__()->GetEndBottomElevationRdId());
    }
}
```

### 角度计算器

`StructureBasicBeamAngleCalculator`用于计算矩形梁的倾斜角度：

```cpp
class StructureBasicBeamAngleCalculator : public GbmpCalculatorBase
{
    DECLARE_CALCULATOR(StructureBasicBeamAngleCalculator, IInstance)
public:
    StructureBasicBeamAngleCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}

    virtual void ReportInputDataIds(std::vector<RegenDataId> & dataIds) const override
    {
        const IInstance* pInstance = GetElement<IInstance>();
        const IElementModelShape* pGRepBehavior = pInstance->GetElementModelShape();

        dataIds.push_back(pGRepBehavior->GetGraphicsElementShapeRdId());
    }

    virtual void Execute() override
    {
        IInstance* pInstance = GetElement<IInstance>();

        double angle(0.0);
        if (StructureInstanceUtils::GetBasicBeamAngle(pInstance, angle))
        {
            UniIdentity paramUid = PARAMETER_UID(BeamAngleBuiltInParameter);
            StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, angle, paramUid);
        }
    }
};
```

该计算器依赖梁的图形表达的RegenDataId。当梁的图形表达发生变化时，会触发该计算器执行，重新计算梁的倾斜角度并更新到参数中。

### 高程计算器

矩形梁有多个高程相关的计算器，用于计算梁的顶部高程、底部高程、起点顶部高程、起点底部高程、终点顶部高程和终点底部高程。以`StructureBasicBeamTopElevationCalculator`为例：

```cpp
class StructureBasicBeamTopElevationCalculator : public StructureBeamElevationCalculatorBase
{
    DECLARE_CALCULATOR(StructureBasicBeamTopElevationCalculator, IInstance)
public:
    StructureBasicBeamTopElevationCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : StructureBeamElevationCalculatorBase(pDoc, outputDataId) {}

    virtual void Execute() override
    {
        IInstance* pInstance = GetElement<IInstance>();

        double topElevation;
        if (StructureInstanceUtils::GetBasicBeamTopElevation(pInstance, topElevation))
        {
            UniIdentity paramUid = PARAMETER_UID(TopElevationBuiltInParameter);
            StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, topElevation, paramUid);
        }
    }
};
```

该计算器依赖梁的图形表达和关联平面的RegenDataId。当梁的图形表达或关联平面发生变化时，会触发该计算器执行，重新计算梁的顶部高程并更新到参数中。

## 变截面梁计算器

变截面梁计算器是通过`StructureVariableSectionBeam`类的`GetCalculators`方法注册的：

```cpp
void StructureVariableSectionBeam::GetCalculators(ICalculatorCollection * calculators) const
{
    GetBaseData__()->GetCalculators(calculators);
    ADD_CALCULATOR(StructureVariableSectionBeamTopElevationCalculator, GetDocument(), GetBaseData__()->GetTopElevationRdId());
    ADD_CALCULATOR(StructureVariableSectionBeamStartBottomElevationCalculator, GetDocument(), GetBaseData__()->GetStartBottomElevationRdId());
    ADD_CALCULATOR(StructureVariableSectionBeamEndBottomElevationCalculator, GetDocument(), GetBaseData__()->GetEndBottomElevationRdId());
    ADD_CALCULATOR(StructureVariableSectionBeamBottomAngleCalculator, GetDocument(), GetBottomAngleRdId());
}
```

### 底部角度计算器

`StructureVariableSectionBeamBottomAngleCalculator`用于计算变截面梁的底部倾斜角度：

```cpp
class StructureVariableSectionBeamBottomAngleCalculator : public StructureBeamElevationCalculatorBase
{
    DECLARE_CALCULATOR(StructureVariableSectionBeamBottomAngleCalculator, IInstance)
public:
    StructureVariableSectionBeamBottomAngleCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : StructureBeamElevationCalculatorBase(pDoc, outputDataId) {}

    virtual void Execute() override
    {
        IInstance* pInstance = GetElement<IInstance>();

        double bottomAngle;
        if (StructureInstanceUtils::GetVariableSectionBeamBottomAngle(pInstance, bottomAngle))
        {
            UniIdentity paramUid = PARAMETER_UID(VariableSectionBeamBottomAngleBuiltInParameter);
            StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, bottomAngle, paramUid);
        }
    }
};
```

该计算器依赖梁的图形表达和关联平面的RegenDataId。当梁的图形表达或关联平面发生变化时，会触发该计算器执行，重新计算变截面梁的底部倾斜角度并更新到参数中。

### 变截面梁高程计算器

变截面梁也有多个高程相关的计算器，用于计算梁的顶部高程、起点底部高程和终点底部高程。以`StructureVariableSectionBeamTopElevationCalculator`为例：

```cpp
class StructureVariableSectionBeamTopElevationCalculator : public StructureBeamElevationCalculatorBase
{
    DECLARE_CALCULATOR(StructureVariableSectionBeamTopElevationCalculator, IInstance)
public:
    StructureVariableSectionBeamTopElevationCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : StructureBeamElevationCalculatorBase(pDoc, outputDataId) {}

    virtual void Execute() override
    {
        IInstance* pInstance = GetElement<IInstance>();

        double topElevation;
        if (StructureInstanceUtils::GetVariableSectionBeamTopElevation(pInstance, topElevation))
        {
            UniIdentity paramUid = PARAMETER_UID(TopElevationBuiltInParameter);
            StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, topElevation, paramUid);
        }
    }
};
```

该计算器依赖梁的图形表达和关联平面的RegenDataId。当梁的图形表达或关联平面发生变化时，会触发该计算器执行，重新计算变截面梁的顶部高程并更新到参数中。
