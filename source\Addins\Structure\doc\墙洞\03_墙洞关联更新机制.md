## 墙洞的父子关系管理机制

墙洞图元在GDMP中实现了父子关系管理机制，通过`ReportParents`和`UpdateForWeakParentDeletion`方法来管理与其他图元的依赖关系。

### Strong Parent vs Weak Parent概念

GDMP中的图元依赖关系分为两种类型：

1. **Strong Parent（强父图元）**：当强父图元被删除时，当前图元也会被系统自动删除
2. **Weak Parent（弱父图元）**：当弱父图元被删除时，当前图元会收到通知，可以进行相应的处理而不被删除

### 墙洞图元的父子关系实现

#### StructureWallHole实现

墙洞图元的父子关系管理实现较为简洁：

<augment_code_snippet path="source/Addins/Structure/StructureModel/WallHole/StructureWallHole.h" mode="EXCERPT">
````cpp
virtual void UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds) override {}
virtual void ReportParents(IElementParentReporter& reporter) const override;
````
</augment_code_snippet>

<augment_code_snippet path="source/Addins/Structure/StructureModel/WallHole/StructureWallHole.cpp" mode="EXCERPT">
````cpp
void gcmp::StructureWallHole::ReportParents(IElementParentReporter& reporter) const
{
}
````
</augment_code_snippet>

### 墙洞与宿主墙体的关系

墙洞与宿主墙体之间的关系通过GDMP的打洞机制（IElementOpening）来管理，而不是通过父子关系：

<augment_code_snippet path="source/Addins/Structure/StructureModel/WallHole/StructureWallHole.cpp" mode="EXCERPT">
````cpp
// 打洞关系建立
IElementOpening::Create(pDoc, pInstance->GetElementId(), pHostInstance->GetElementId());
````
</augment_code_snippet>

### 设计特点

墙洞的父子关系管理具有以下特点：

1. **简洁性**：墙洞不直接依赖其他图元，因此父子关系管理实现为空
2. **打洞机制**：墙洞与宿主墙体的关系通过专门的打洞机制管理，而非父子关系
3. **独立性**：墙洞的生命周期由打洞机制控制，不依赖于父子关系管理

### 与其他图元的对比

与板洞等图元不同，墙洞不需要管理轮廓线等弱引用关系，因为墙洞的形状由族参数直接控制，而不是通过外部轮廓线定义。

墙洞实现了多个计算器，用于处理墙洞的参数计算和关联更新。这些计算器通过GDMP的关联更新机制，实现了墙洞与墙体之间的参数联动。

## 墙洞厚度参数计算器
`WallHoleThicknessParameterCalculator`用于计算墙洞的厚度参数，确保墙洞的厚度与墙体厚度一致：

```cpp
class WallHoleThicknessParameterCalculator : public GbmpCalculatorBase
{
    DECLARE_CALCULATOR(WallHoleThicknessParameterCalculator, IElement)
public:
    WallHoleThicknessParameterCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}
    virtual void ReportInputDataIds(std::vector<RegenDataId> & oInputDatas) const override;
    virtual void Execute() override;

public:
    static bool UpdateWallHoleThicknessParameters(IElement *pElement);
};
```

### 计算器注册
在`StructureWallHole::GetCalculators`方法中注册了该计算器：

```cpp
void gcmp::StructureWallHole::GetCalculators(ICalculatorCollection * calculators) const
{
    const IElement* pElement = this->GetOwnerElement();
    const IElementParameters* pElementParameters = pElement->GetElementParameters();

    ADD_CALCULATOR_OVERRIDE_REGENDATA(WallHoleThicknessParameterCalculator, GetDocument(), pElementParameters->GetDriveParameterRdId());

    // ...
}
```

### 输入数据依赖
`ReportInputDataIds`方法报告了该计算器依赖的输入数据ID：

```cpp
void WallHoleThicknessParameterCalculator::ReportInputDataIds(std::vector<RegenDataId> & oInputDatas) const
{
    const IInstance* pElement = GetElement<IInstance>();

    ElementId wallId = pElement->GetHostElementId();
    if (!wallId.IsValid())
    {
        return;
    }

    //墙洞的实例依赖数据收集
    {
        oInputDatas.push_back(pElement->GetBasicInformation()->GetTypeIdRdId());
    }

    //墙洞的类型依赖数据收集
    {
        const IElementParameters* pElementParameters = pElement->GetBasicInformation()->GetType()->GetElementParameters();
        oInputDatas.push_back(pElementParameters->GetDriveParameterRdId());
    }
}
```

这里报告了墙洞实例和类型的依赖数据，当这些数据发生变化时，会触发计算器执行。

### 执行计算
`Execute`方法执行实际的计算：

```cpp
void WallHoleThicknessParameterCalculator::Execute()
{
    IElement* pElement = GetTarget();

    WallHoleThicknessParameterCalculator::UpdateWallHoleThicknessParameters(pElement);
}
```

`UpdateWallHoleThicknessParameters`方法实现了具体的厚度计算逻辑：

```cpp
bool WallHoleThicknessParameterCalculator::UpdateWallHoleThicknessParameters(IElement *pElement)
{
    // 获取墙洞实例和主体墙实例
    IInstance* pHole = quick_cast<IInstance>(pElement);
    IDocument* pDoc = pHole->GetDocument();
    const IInstance* pHost = dynamic_cast<IInstance*>(pDoc->GetElement(pHole->GetHostInstanceId()));

    // 获取墙的厚度参数
    OwnerPtr<IParameter> opHostThicknessParam = ELEMENT_PARAMETER(pHostType, CoreThicknessBuiltInParameter);
    double hostThickness = opHostThicknessParam->GetValueAsDouble();
    double holeThickness = hostThickness;

    // 对于弧墙，需要特殊处理厚度计算
    const IGraphicsCurve3d* pGCurve = curves[0].get();
    const IArc3d* pArc = quick_cast<IArc3d>(pGCurve->GetGeometry());
    if (pArc)
    {
        // 弧墙厚度计算逻辑
        // ...
        holeThickness += delta * 2;
    }

    // 设置墙洞厚度参数
    OwnerPtr<IParameter> opHoleThicknessParam = ELEMENT_PARAMETER(pHole, WallHoleThicknessBuiltInParameter);
    opHoleThicknessParam->SetValueAsDouble(holeThickness);
    pHole->GetElementParameters()->SetParameter(opHoleThicknessParam.get());

    return true;
}
```

当墙的厚度参数发生变化时，会触发该计算器执行，更新墙洞的厚度参数，确保墙洞的厚度与墙体厚度一致。对于弧墙，还会根据弧墙的半径和墙洞的宽度进行特殊计算，确保墙洞能够正确穿透弧墙。

## 墙洞关联楼层计算器
`WallHoleAssociatedLevelCalculator`用于计算墙洞所属的楼层，确保墙洞与墙体所属的楼层一致：

```cpp
class WallHoleAssociatedLevelCalculator : public GbmpCalculatorBase
{
    DECLARE_CALCULATOR(WallHoleAssociatedLevelCalculator, IElement)
public:
    WallHoleAssociatedLevelCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}
    virtual void ReportInputDataIds(std::vector<RegenDataId> & oInputDatas) const override;
    virtual void Execute() override;
};
```

### 计算器注册
在`StructureWallHole::GetCalculators`方法中注册了该计算器：

```cpp
void gcmp::StructureWallHole::GetCalculators(ICalculatorCollection * calculators) const
{
    // ...

    ElementId wallHoleId = pElement->GetElementId();
    const IInstance* pWallHoleInstance = quick_cast<const IInstance>(GetDocument()->GetElement(wallHoleId));
    const IStructureInstanceBuildingStoreyData* pWallHoleStoreyData = IStructureInstanceBuildingStoreyData::Get(pWallHoleInstance);
    if (pWallHoleStoreyData != nullptr)
    {
        RegenDataId wallHoleStoreyDataId = pWallHoleStoreyData->GetBuildingStoreyIdRdId();
        ADD_CALCULATOR(WallHoleAssociatedLevelCalculator, GetDocument(), wallHoleStoreyDataId);
    }
}
```

### 输入数据依赖
`ReportInputDataIds`方法报告了该计算器依赖的输入数据ID：

```cpp
void WallHoleAssociatedLevelCalculator::ReportInputDataIds(std::vector<RegenDataId> & oInputDatas) const
{
    const IInstance* pElement = GetElement<IInstance>();

    ElementId wallId = pElement->GetHostElementId();
    if (!wallId.IsValid())
    {
        return;
    }

    //墙洞的Host主体依赖数据收集
    {
        const IInstance* pHostWallInstance = quick_cast<const IInstance>(GetDocument()->GetElement(wallId));
        const IStructureInstanceBuildingStoreyData* pWallStoreyData = IStructureInstanceBuildingStoreyData::Get(pHostWallInstance);
        RegenDataId wallStoreyDataId = pWallStoreyData->GetBuildingStoreyIdRdId();
        oInputDatas.push_back(wallStoreyDataId);
    }
}
```

这里报告了墙体所属楼层的依赖数据，当墙体的所属楼层发生变化时，会触发计算器执行。

### 执行计算
`Execute`方法执行实际的计算：

```cpp
void WallHoleAssociatedLevelCalculator::Execute()
{
    const IInstance* pWallHoleInstance = GetElement<IInstance>();

    ElementId wallId = pWallHoleInstance->GetHostElementId();

    const IInstance* pHostWallInstance = quick_cast<const IInstance>(GetDocument()->GetElement(wallId));

    OwnerPtr<IParameter> opWallLevelParam = ELEMENT_PARAMETER(pHostWallInstance, BuildingStoreyBuiltInParameter);
    ElementId wallLevelId = opWallLevelParam->GetValueAsElementId();

    bool status = StructureInstanceLevelUtils::SetInstanceStoreyLevel(GetDocument(), pWallHoleInstance->GetElementId(), wallLevelId);
}
```

当墙体的所属楼层发生变化时，会触发该计算器执行，更新墙洞的所属楼层，确保墙洞与墙体所属的楼层一致。这样，当用户移动墙体到不同楼层时，墙洞也会自动跟随墙体移动到相应的楼层。

## 关联更新机制总结
墙洞的计算器机制通过GDMP的关联更新系统实现了以下功能：

1. **墙厚变化引起墙洞厚度更新**：当墙的厚度参数发生变化时，`WallHoleThicknessParameterCalculator`会自动更新墙洞的厚度参数，确保墙洞能够正确穿透墙体。

2. **墙所属楼层变化引起墙洞楼层更新**：当墙体的所属楼层发生变化时，`WallHoleAssociatedLevelCalculator`会自动更新墙洞的所属楼层，确保墙洞与墙体所属的楼层一致。

这些关联更新机制确保了墙洞与墙体之间的参数联动，使得墙洞能够随着墙体的变化而自动调整，提高了建模的效率和准确性。

