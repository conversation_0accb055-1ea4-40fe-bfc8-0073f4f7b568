板洞的创建和编辑共用同一个编辑模式类型`StructureEditModeTypes::StructurePlateHoleModeType`和编辑模式行为类`IStructurePlateHoleEditModeBehavior`，但通过不同的命令触发：

- `CmdCreateStructurePlateHole`：用于创建新的板洞
- `CmdEditStructurePlateHoleProfile`：用于编辑现有的板洞

板洞编辑使用与创建相同的编辑模式类`StructurePlateHoleEditModeBehavior`和UI交互类`UIStructurePlateHoleProfileEditModeBehavior`。数据层部分详见[板洞的编辑更新机制](06_板洞的编辑更新机制.md)中的"板洞的编辑模式实现与更新机制"部分。

创建和编辑板洞的主要区别在于启动编辑模式时传入的参数不同：

- 创建板洞时，`isCreating`参数为`true`，编辑元素ID为`InvalidID`
- 编辑板洞时，`isCreating`参数为`false`，编辑元素ID为现有板洞的ID

## 板洞的创建命令

板洞的创建通过`CmdCreateStructurePlateHole`命令类实现：

```cpp
class CmdCreateStructurePlateHole :public GbmpCommandBase
{
};
```

这个命令类继承自`GbmpCommandBase`，实现了板洞创建的基本功能。

板洞的创建命令执行流程如下：

1. 检查运行环境
2. 创建内建板洞族（如果不存在）
   - StructureFamilyUiUtils::CreateFamilies
3. 提示用户选择宿主板
4. 进入板洞编辑模式
   - 创建编辑模式：`IEditMode::Create(pDoc, IStructurePlateHoleEditModeBehavior::Create())`
   - 设置编辑模式参数：`pProfileEDBehavior->SetEditingFamilyId(m_familyId)`等
   - 启动编辑模式：`pProfileEditMode->StartEditing(pDoc, ElementId::InvalidID, true, false)`，注意`isCreating`参数为`true`
5. 启动轮廓线绘制命令：`ID_CMD_STRUCTURE_DRAW_STRAIGHT_PROFILE`

这个流程通过以下代码实现：

```cpp
OwnerPtr<IAction> CmdCreateStructurePlateHole::ExecuteCommand(const CommandParameters& cmdParams)
{
    // 检查运行环境
    if (!CheckRunTimeEnvironment())
    {
        return nullptr;
    }

    // 获取当前文档和UI文档
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    IUiDocument* pUIDoc = UiDocumentViewUtils::GetCurrentUiDocument();

    // 创建内建板洞族
    StructureFamilyUiUtils::FamilyCreators defaultFamilyCreators = this->GetDefaultFamilyCreators();
    StructureFamilyUiUtils::CreateFamilies(pDoc, defaultFamilyCreators);

    // 高亮命令按钮
    IRibbon *pRibbon = pMainWindow->GetRibbon();
    pRibbon->SetCustomHighLightControl(std::vector<std::wstring>{GetCommandId()});

    // 选择宿主板
    ElementId hostInstanceId = GetPickedResultId();
    if (hostInstanceId.GetIsValidId())
    {
        // 开始事务组
        UserTransactionGroupUtils::Start(pDoc, GBMP_TR(L"创建洞口轮廓实例"), groupId);
        OwnerPtr<IUserTransaction> opUt = IUserTransaction::Create(pDoc, GBMP_TR(L"开始绘制洞口"));

        // 创建编辑模式
        IEditMode* pProfileEditMode = IEditMode::Get(pDoc, StructureEditModeTypes::StructurePlateHoleModeType);
        if (pProfileEditMode == nullptr)
        {
            pProfileEditMode = IEditMode::Create(pDoc, IStructurePlateHoleEditModeBehavior::Create());
        }

        // 设置编辑模式参数
        IStructurePlateHoleEditModeBehavior* pProfileEDBehavior = quick_cast<IStructurePlateHoleEditModeBehavior>(pProfileEditMode->GetEditModeBehavior());
        pProfileEDBehavior->SetEditingFamilyId(m_familyId);
        pProfileEDBehavior->SetEditingInstanceTypeId(m_instanceTypeId);
        pProfileEDBehavior->SetHostId(hostInstanceId);

        // 启动编辑模式，isCreating=true表示创建新板洞
        pProfileEditMode->StartEditing(pDoc, ElementId::InvalidID, true, false);

        opUt->Commit();

        // 启动画直轮廓线的命令
        ICommandManager* pCommandManager = ICommandManager::Get();
        pCommandManager->SendCommand(ID_CMD_STRUCTURE_DRAW_STRAIGHT_PROFILE);
    }

    return nullptr;
}
```

### 选择宿主板
板洞创建过程中，首先需要选择宿主板（楼板或筏板）：

```cpp
ElementId CmdCreateStructurePlateHole::GetPickedResultId() const
{
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();

    // 创建拾取过滤器，只允许选择楼板和筏板
    OwnerPtr<StructurePlateHolePickFilter> opPickFilter = NEW_AS_OWNER_PTR(StructurePlateHolePickFilter, pDoc);

    // 执行交互式选择
    pickerResult = InteractivePicker::Select(false, false, TransferOwnership(opPickFilter),
        L"选择将要放置板洞的板", L"选择将要放置板洞的板", pickedPt, nodeReferences);

    if (pickerResult.FinishStatus == InteractivePicker::PickerFinishStatus::Successful)
    {
        return nodeReferences[0]->GetElementId();
    }

    return ElementId::InvalidID;
}
```

`StructurePlateHolePickFilter`类实现了拾取过滤器，只允许选择楼板和筏板：

```cpp
bool StructurePlateHolePickFilter::AllowElement(const ElementId& elementId) const
{
    //允许拾取楼板和筏板
    IInstance* pInstance = quick_cast<IInstance>(m_pDoc->GetElement(elementId));
    if (pInstance == nullptr)
        return false;

    if (!pInstance->GetBasicInformation()->GetCategoryUid().IsValid())
        return false;

    if (pInstance->GetBasicInformation()->GetCategoryUid() == BuiltInCategoryUniIdentities::BICU_STRUCTURE_FLOOR ||
        pInstance->GetBasicInformation()->GetCategoryUid() == BuiltInCategoryUniIdentities::BICU_STRUCTURE_FOUNDATION)
        return true;

    return false;
}
```

### 进入轮廓编辑模式
选择宿主板后，系统会启动轮廓线绘制命令，用户可以绘制任意形状的闭合轮廓作为板洞的形状。绘制过程中，系统会进入特定的编辑模式。

板洞的编辑模式由`StructurePlateHoleEditModeBehavior`（数据层）和`UIStructurePlateHoleProfileEditModeBehavior`（UI交互层）两个类实现。数据层部分详见[板洞的编辑更新机制](06_板洞的编辑更新机制.md)中的"板洞的编辑模式实现与更新机制"部分。

UI交互层由`UIStructurePlateHoleProfileEditModeBehavior`类实现：

```cpp
class UIStructurePlateHoleProfileEditModeBehavior : public gcmp::IUiEditModeBehavior
{
public:
    UIStructurePlateHoleProfileEditModeBehavior(gcmp::IDocument* pDoc, int uiDocID, std::wstring editModeType);

public:
    virtual void OnEnterEditMode(gcmp::IDocument* pDoc) override;
    virtual void OnBeforeExitEditMode() override;
    virtual bool IsCommandEnabled(const std::wstring& cmdString) const override;

private:
    gcmp::OwnerPtr<gcmp::IRibbonContextualPage> m_opRibbonContextualPage;
};
```

这个类继承自`IUiEditModeBehavior`，主要负责：

- 设置编辑模式下的Ribbon上下文页
- 显示和隐藏编辑模式框
- 控制命令在编辑模式下的可用性

```cpp
void UIStructurePlateHoleProfileEditModeBehavior::OnEnterEditMode(gcmp::IDocument* pDoc)
{
    StructureUIEditModeUtil::SetupDrawProfileContext(m_opRibbonContextualPage.get(), GBMP_TR(L"绘制板洞"));
    // 编辑模式框
    if (IMainWindow * pUIMainWnd = IMainWindow::GetMainWindow())
    {
        pUIMainWnd->ShowApplyOrCancelGui(GBMP_TR(L"编辑轮廓"), ID_CMD_EDIT_STRUCTURE_PLATE_HOLE_FINISH, ID_CMD_EDIT_PROFILE_CANCEL);
    }
}
```

### 完成板洞创建
绘制完轮廓后，通过`CmdFinishDrawStructurePlateHoleProfile`命令完成板洞的创建：

```cpp
OwnerPtr<IAction> CmdFinishDrawStructurePlateHoleProfile::ExecuteCommand(const CommandParameters& cmdParams)
{
    // 获取当前文档和编辑模式
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    IModelView* pModelView = UiDocumentViewUtils::GetCurrentModelView();
    const IEditMode* pProfileEditMode = IEditMode::Get(pDoc, StructureEditModeTypes::StructurePlateHoleModeType);

    // 获取编辑模式行为
    const IStructurePlateHoleEditModeBehavior* pProfileEDBehavior = quick_cast<IStructurePlateHoleEditModeBehavior>(pProfileEditMode->GetEditModeBehavior());

    // 获取创建参数
    bool bIsCreating = pProfileEditMode->GetIsCreating();
    ElementId familyId = pProfileEDBehavior->GetEditingFamilyId();
    ElementId instanceTypeId = pProfileEDBehavior->GetEditingInstanceTypeId();
    ElementId floorId = pProfileEDBehavior->GetHostId();

    // 获取轮廓线
    std::vector<IModelLine*> profileCurves;
    bool bOk = StructureInstanceCmdUtils::GetProfileCurves(pDoc, profileCurves);

    if (bIsCreating)
    {
        // 创建板洞
        OwnerPtr<StructurePlateHoleElementInput> opInput = NEW_AS_OWNER_PTR(StructurePlateHoleElementInput);
        OwnerPtr<IUserTransaction> opUt = IUserTransaction::Create(pDoc, GBMP_TR(L"完成编辑轮廓实例"));

        // 退出编辑模式
        IEditMode* pEditMode = IEditMode::Get(pDoc, StructureEditModeTypes::StructurePlateHoleModeType);
        pEditMode->EndEditing(pDoc);

        // 生成输入数据
        bool bOk = StructureInstanceCmdUtils::GenerateStructurePlateHoleInput(pDoc, pModelView, GetCategory(), GetCategorySecondKey(),
            familyId, instanceTypeId, *opInput);

        // 设置宿主板和轮廓线
        opInput->HostId = floorId;
        opInput->ProfileCurves = profileCurves;

        // 创建板洞
        IStructurePlateHole* pPlateHole = IStructurePlateHole::Create(opInput.get());

        // 清空选择集和高亮
        ISelection::Get()->Clear(pDoc);
        IHighlights::Get()->Clear();

        opUt->Commit();
    }
    else
    {
        // 编辑现有板洞
        // ...
    }

    // 提交事务组
    UserTransactionGroupUtils::CommitLatestActive(pDoc);

    // 更新视图
    pUIDoc->UpdateView();

    return nullptr;
}
```

## 板洞的编辑功能

板洞的编辑功能与创建功能共用同一个编辑模式和完成命令，但通过不同的入口命令进入：

- 创建板洞时，通过`CmdCreateStructurePlateHole`命令进入编辑模式，`isCreating`参数为`true`
- 编辑板洞时，通过`CmdEditStructurePlateHoleProfile`命令进入编辑模式，`isCreating`参数为`false`

`CmdEditStructurePlateHoleProfile`命令进入编辑模式的主要流程如下：

1. 获取当前文档和选中的板洞实例：
   ```cpp
   IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
   IInstance* pEditedInstance = GetSelectedInstance();
   ```

2. 获取板洞的族和类型信息，用于后续编辑模式设置：
   ```cpp
   const IFamily* pFamily = pEditedInstance->GetFamily();
   ```

3. 获取编辑模式对象并设置参数：
   ```cpp
   IEditMode* pStructurePlateHoleEditMode = IEditMode::Get(pDoc, StructureEditModeTypes::StructurePlateHoleModeType);
   IStructurePlateHoleEditModeBehavior* pProfileBehavior = quick_cast<IStructurePlateHoleEditModeBehavior>(
       pStructurePlateHoleEditMode->GetEditModeBehavior());
   pProfileBehavior->SetEditingFamilyId(pFamily->GetElementId());
   pProfileBehavior->SetEditingInstanceTypeId(pEditedInstance->GetBasicInformation()->GetType()->GetElementId());
   ```

4. 启动编辑模式，传入板洞实例ID，并指定为编辑操作：
   ```cpp
   pStructurePlateHoleEditMode->StartEditing(pDoc, editedId, false, false);
   ```

5. 隐藏原始板洞实例，显示轮廓线，并启动轮廓线绘制命令：
   ```cpp
   pStatus->SetIsVisible(false); // 隐藏原实例
   pCommandManager->SendCommand(ID_CMD_STRUCTURE_DRAW_STRAIGHT_PROFILE);
   ```

当进入编辑模式后，UI界面会显示"编辑轮廓"的编辑模式框，并激活相应的上下文功能区，方便用户编辑板洞轮廓。

### 进入板洞编辑模式

板洞编辑通过`CmdEditStructurePlateHoleProfile`命令类实现，该命令用于选中现有板洞后进入编辑模式：

```cpp
class CmdEditStructurePlateHoleProfile : public CmdEditDrawProfileBase
{
public:
    CmdEditStructurePlateHoleProfile(const std::wstring& cmdId = ID_CMD_EDIT_STRUCTURE_PLATE_HOLE);
    virtual ~CmdEditStructurePlateHoleProfile() {}

public:
    virtual OwnerPtr<IAction> ExecuteCommand(const CommandParameters& cmdParams) override;
    virtual UniIdentity GetCategory() const override;

private:
    void GetProfiles(ElementId instanceId, std::vector<IModelLine*>& profiles) const;
};
```

命令执行流程与创建板洞类似，主要步骤包括：

1. 获取选中的板洞实例
2. 创建或获取编辑模式
3. 设置编辑模式参数（族ID、实例类型ID等）
4. 启动编辑模式，关键是`isCreating=false`
5. 隐藏原始板洞实例
6. 启动绘制直线轮廓的命令

### 完成板洞编辑

完成板洞编辑同样使用`CmdFinishDrawStructurePlateHoleProfile`命令，但会根据`bIsCreating`参数执行不同的逻辑。对于编辑现有板洞的情况，代码实现如下：

```cpp
else
{
    ElementId instanceId = pProfileEditMode->GetEditingElementId();
    OwnerPtr<StructurePlateHoleEditElementInput> opInput = NEW_AS_OWNER_PTR(StructurePlateHoleEditElementInput);
    opInput->Document = pDoc;
    opInput->InstanceId = instanceId;
    opInput->ProfileCurves = profileCurves;

    OwnerPtr<IUserTransaction> opUt = IUserTransaction::Create(pDoc, GBMP_TR(L"完成编辑轮廓实例"));
    {
        IEditMode* pEditMode = IEditMode::Get(pDoc, StructureEditModeTypes::StructurePlateHoleModeType);
        // 退出编辑模式，确保Instance Calculators可以开始工作
        pEditMode->EndEditing(pDoc);

        IStructurePlateHole* pPlateHole = IStructurePlateHole::Edit(opInput.get());

        if (!pPlateHole)
        {
            UserTransactionGroupUtils::AbortLatestActive(pDoc);
            return nullptr;
        }
        ISelection::Get()->Clear(pDoc);
        IHighlights::Get()->Clear();
    }
    opUt->Commit();
}
```

主要步骤包括：

1. 获取正在编辑的板洞ID：`instanceId = pProfileEditMode->GetEditingElementId()`
2. 创建编辑输入数据对象`StructurePlateHoleEditElementInput`，包含：
   - 文档指针：`opInput->Document = pDoc`
   - 实例ID：`opInput->InstanceId = instanceId`
   - 新的轮廓线：`opInput->ProfileCurves = profileCurves`
3. 创建用户事务：`IUserTransaction::Create(pDoc, GBMP_TR(L"完成编辑轮廓实例"))`
4. 退出编辑模式：`pEditMode->EndEditing(pDoc)`，确保实例计算器可以开始工作
5. 调用`IStructurePlateHole::Edit`方法更新板洞
6. 错误处理：如果编辑失败，中止最近的活动事务组
7. 清空选择集和高亮
8. 提交事务

#### 创建与编辑板洞的主要区别

创建板洞和编辑板洞在`CmdFinishDrawStructurePlateHoleProfile::ExecuteCommand`中的主要区别如下：

| 功能 | 创建板洞 | 编辑板洞 |
|------|---------|---------|
| 输入类型 | `StructurePlateHoleElementInput` | `StructurePlateHoleEditElementInput` |
| 调用方法 | `IStructurePlateHole::Create` | `IStructurePlateHole::Edit` |
| 参数设置 | 需要设置族ID、实例类型ID、宿主板ID等 | 只需设置实例ID和新轮廓线 |
| 错误处理 | 无特殊错误处理 | 如果编辑失败，中止事务组 |

#### 编辑板洞的特殊处理

编辑板洞时有一些特殊的处理逻辑：

1. **使用专用的编辑输入类**：编辑板洞使用`StructurePlateHoleEditElementInput`类，而不是创建时使用的`StructurePlateHoleElementInput`类。编辑输入类更简单，只需要提供文档、实例ID和新轮廓线。

2. **错误处理机制**：编辑板洞时增加了错误处理机制，如果`IStructurePlateHole::Edit`返回空指针，表示编辑失败，会中止当前事务组：

   ```cpp
   if (!pPlateHole)
   {
       UserTransactionGroupUtils::AbortLatestActive(pDoc);
       return nullptr;
   }
   ```

3. **保留原有属性**：编辑板洞时，系统会保留原有板洞的属性（如族、类型等），只更新轮廓线。这与创建板洞时需要设置所有属性不同。

4. **事务处理**：编辑和创建都使用事务组，但编辑操作有额外的错误回滚机制，确保在编辑失败时能够恢复到原始状态。
